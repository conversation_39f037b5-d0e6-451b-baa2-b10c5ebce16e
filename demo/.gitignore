# Compiled class file
*.class

# Log files
*.log
logs/
log/

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar
!.mvn/wrapper/maven-wrapper.properties

# Maven specific exclusions
!**/src/main/**/target/
!**/src/test/**/target/

# Spring Tool Suite (STS)
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

# Spring Boot
spring-boot-devtools.properties

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

# VS Code
.vscode/

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.loadpath
.recommenders
.externalToolBuilders/
*.launch
.factorypath
.buildpath
.target
.tern-project
.texlipse
.cache-main
.scala_dependencies
.worksheet

# Database files
*.db
*.sqlite
*.sqlite3
*.h2.db

# Configuration files with sensitive data (exclude actual config, include examples)
#db.properties
application-local.properties
application-dev.properties
application-prod.properties
!db.properties.example
!application.properties.example

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*~

# Server specific (Tomcat server configs should be tracked but runtime data excluded)
Servers/**/work/
Servers/**/logs/
Servers/**/temp/
Servers/**/wtpwebapps/

# Test output
target/surefire-reports/
target/failsafe-reports/

# Generated sources
target/generated-sources/
target/generated-test-sources/
target/m2e-wtp/

# Help documentation
HELP.md