package com.kskyb.stat.test;

import com.kskyb.stat.dao.StatsDao;
import com.kskyb.stat.model.Search;
import com.kskyb.stat.model.Stats;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;

/**
 * selectCompanyStats 쿼리 테스트 클래스
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {
    "classpath:spring/spring-context.xml",
    "classpath:spring/servlet-context.xml"
})
@WebAppConfiguration
public class CompanyStatsQueryTest {

    @Autowired
    private StatsDao statsDao;

    @Test
    public void testSelectCompanyStats() {
        try {
            System.out.println("=== selectCompanyStats 쿼리 테스트 시작 ===");
            
            // 테스트용 검색 조건 설정
            Search search = new Search();
            search.setSDate("2024-01-01");  // 시작일
            search.setEDate("2024-12-31");  // 종료일
            
            System.out.println("검색 조건:");
            System.out.println("- 시작일: " + search.getSDate());
            System.out.println("- 종료일: " + search.getEDate());
            System.out.println("- DB 시작일: " + search.getDbSDate());
            System.out.println("- DB 종료일: " + search.getDbEDate());
            
            // 쿼리 실행
            List<Stats> results = statsDao.selectCompanyStats(search);
            
            System.out.println("=== 쿼리 실행 결과 ===");
            System.out.println("총 조회 건수: " + results.size());
            
            // 결과 데이터 출력 (최대 5건)
            int count = 0;
            for (Stats stat : results) {
                if (count >= 5) {
                    System.out.println("... (총 " + results.size() + "건 중 5건만 표시)");
                    break;
                }
                
                System.out.println("\n=== 결과 " + (++count) + " ===");
                System.out.println("업체명: " + stat.getCName());
                System.out.println("파트너ID: " + stat.getPtnId());
                System.out.println("날짜: " + stat.getCDate());
                System.out.println("전체건수: " + stat.getTCount());
                System.out.println("성공건수: " + stat.getSCount());
                System.out.println("실패건수: " + stat.getFCount());
                
                // 메시지 타입별 통계 출력
                if (stat.getSmsTotal() > 0) {
                    System.out.println("SMS - 전체: " + stat.getSmsTotal() + ", 성공: " + stat.getSmsSuccess() + ", 실패: " + stat.getSmsFail());
                }
                if (stat.getLmsTotal() > 0) {
                    System.out.println("LMS - 전체: " + stat.getLmsTotal() + ", 성공: " + stat.getLmsSuccess() + ", 실패: " + stat.getLmsFail());
                }
                if (stat.getMmsTotal() > 0) {
                    System.out.println("MMS - 전체: " + stat.getMmsTotal() + ", 성공: " + stat.getMmsSuccess() + ", 실패: " + stat.getMmsFail());
                }
                if (stat.getAlimtalkTotal() > 0) {
                    System.out.println("알림톡 - 전체: " + stat.getAlimtalkTotal() + ", 성공: " + stat.getAlimtalkSuccess() + ", 실패: " + stat.getAlimtalkFail());
                }
                if (stat.getRcsTemplateTotal() > 0) {
                    System.out.println("RCS Template - 전체: " + stat.getRcsTemplateTotal() + ", 성공: " + stat.getRcsTemplateSuccess() + ", 실패: " + stat.getRcsTemplateFail());
                }
            }
            
            System.out.println("\n=== 쿼리 테스트 완료 ===");
            
        } catch (Exception e) {
            System.err.println("쿼리 테스트 실패: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testSelectCompanyStatsWithFilters() {
        try {
            System.out.println("=== 필터링 조건 포함 테스트 시작 ===");
            
            // 특정 업체로 필터링하는 테스트
            Search search = new Search();
            search.setSDate("2024-01-01");
            search.setEDate("2024-12-31");
            search.setPtnId("1");  // 특정 파트너 ID로 필터링
            
            System.out.println("검색 조건:");
            System.out.println("- 시작일: " + search.getSDate());
            System.out.println("- 종료일: " + search.getEDate());
            System.out.println("- 파트너ID: " + search.getPtnId());
            
            List<Stats> results = statsDao.selectCompanyStats(search);
            
            System.out.println("=== 필터링 결과 ===");
            System.out.println("총 조회 건수: " + results.size());
            
            for (Stats stat : results) {
                System.out.println("업체명: " + stat.getCName() + ", 파트너ID: " + stat.getPtnId() + ", 전체건수: " + stat.getTCount());
            }
            
        } catch (Exception e) {
            System.err.println("필터링 테스트 실패: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
