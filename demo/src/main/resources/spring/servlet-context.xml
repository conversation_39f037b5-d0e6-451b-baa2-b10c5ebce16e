<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <!-- 컨트롤러만 스캔 -->
    <context:component-scan base-package="com.kskyb.stat.controller" />
    
    <!-- MVC 어노테이션 지원 -->
    <mvc:annotation-driven />
    
    <!-- 정적 리소스 처리 -->
    <mvc:resources mapping="/css/**" location="/css/" />
    <mvc:resources mapping="/js/**" location="/js/" />
    <mvc:resources mapping="/images/**" location="/images/" />
    <mvc:resources mapping="/assets/**" location="/assets/" />
    
    <!-- ViewResolver 설정 -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/views/" />
        <property name="suffix" value=".jsp" />
        <property name="contentType" value="text/html; charset=UTF-8" />
    </bean>
    
    <!-- 인터셉터 설정 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**" />
            <mvc:exclude-mapping path="/login" />
            <mvc:exclude-mapping path="/auth/**" />
            <mvc:exclude-mapping path="/css/**" />
            <mvc:exclude-mapping path="/js/**" />
            <mvc:exclude-mapping path="/images/**" />
            <mvc:exclude-mapping path="/assets/**" />
            <mvc:exclude-mapping path="/" />
            <mvc:exclude-mapping path="/index.jsp" />
            <bean class="com.kskyb.stat.interceptor.LoginInterceptor" />
        </mvc:interceptor>
    </mvc:interceptors>
    
    <!-- 멀티파트 리졸버 (파일 업로드용) - Spring 6.x 호환 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.support.StandardServletMultipartResolver">
        <!-- 표준 Servlet API 사용 - web.xml에서 multipart-config 설정 필요 -->
    </bean>

</beans>