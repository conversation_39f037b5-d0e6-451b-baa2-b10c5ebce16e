<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:mybatis-spring="http://mybatis.org/schema/mybatis-spring"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd 
                           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
                           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
                           http://mybatis.org/schema/mybatis-spring http://mybatis.org/schema/mybatis-spring.xsd">

    <!-- 애노테이션 설정 활성화 -->
    <context:annotation-config />
    
    <!-- 트랜잭션 애노테이션 활성화 -->
    <tx:annotation-driven transaction-manager="transactionManager" />
    
    <!-- 서비스, 기타 컴포넌트 스캔 (컨트롤러 제외) -->
    <context:component-scan base-package="com.kskyb.stat">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:exclude-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
        <!-- DatabaseConfig 클래스는 제외 (XML로 대체) -->
        <context:exclude-filter type="assignable" expression="com.kskyb.stat.config.DatabaseConfig"/>
    </context:component-scan>
    
    <!-- 프로퍼티 파일 로드 -->
    <context:property-placeholder location="classpath:db.properties" ignore-resource-not-found="true" />

    <!-- 데이터소스 설정 -->
    <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${jdbc.driverClassName}" />
        <property name="url" value="${jdbc.url}" />
        <property name="username" value="${jdbc.username}" />
        <property name="password" value="${jdbc.password}" />
        <property name="initialSize" value="3" />
        <property name="maxIdle" value="2" />
        <property name="maxTotal" value="60" />
        <property name="removeAbandonedOnBorrow" value="true" />
    </bean>

    <!-- MyBatis SqlSessionFactory 설정 -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource" />
        <property name="configLocation" value="classpath:/mybatis/mybatis-config.xml" />
        <property name="mapperLocations" value="classpath:com/kskyb/stat/dao/mapper/*.xml" />
    </bean>

    <!-- MyBatis SqlSessionTemplate 설정 -->
    <bean id="sqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg ref="sqlSessionFactory" />
    </bean>

    <!-- MyBatis Mapper 스캔 -->
    <mybatis-spring:scan base-package="com.kskyb.stat.dao" />

    <!-- JdbcTemplate 설정 -->
    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource" />
    </bean>

    <!-- 트랜잭션 매니저 설정 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource" />
    </bean>

</beans>