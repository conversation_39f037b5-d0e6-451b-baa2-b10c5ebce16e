<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="true" />
        <setting name="lazyLoadingEnabled" value="true" />
        <setting name="aggressiveLazyLoading" value="false" />
        <setting name="multipleResultSetsEnabled" value="true" />
        <setting name="useColumnLabel" value="true" />
        <setting name="useGeneratedKeys" value="false" />
        <setting name="autoMappingBehavior" value="PARTIAL" />
        <setting name="defaultStatementTimeout" value="25000" />
        <setting name="defaultExecutorType" value="REUSE" />
        <setting name="mapUnderscoreToCamelCase" value="true" />
        <setting name="jdbcTypeForNull" value="NULL" />
    </settings>

    <!-- typeAliases는 추후 추가 예정 -->
    <typeAliases>
        <!-- 예시: <typeAlias type="com.kskyb.stat.model.Stats" alias="Stats"/> -->
        <!-- 예시: <typeAlias type="com.kskyb.stat.model.Search" alias="Search"/> -->
    </typeAliases>

</configuration>