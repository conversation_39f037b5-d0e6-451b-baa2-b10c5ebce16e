package com.kskyb.stat.dao;

import com.kskyb.stat.model.Stats;
import com.kskyb.stat.model.Search;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public interface StatsDao {

	// 통계 일반 | 테이블: 통합 로그 | 옵션: 월별
	List<Stats> selectUnionLogOpMonth(Search search);
	// 통계 일반 | 테이블: 월별 로그 | 옵션: 월별
	Stats selectMonthlyLogOpMonth(Search search);
	// 통계 일반 | 테이블: 통합 로그 | 옵션: 일별
	List<Stats> selectUnionLogOpDay(Search search);
	// 통계 일반 | 테이블: 월별 로그 | 옵션: 일별
	List<Stats> selectMonthlyLogOpDay(Search search);

	// 통계 결과코드별 | 테이블: 통합 로그 | 옵션: 월별
	List<Stats> selectUnionLogByResultOpMonth(Search search);
	// 통계 결과코드별 | 테이블: 월별 로그 | 옵션: 월별
	List<Stats> selectMonthlyLogByResultOpMonth(Search search);
	// 통계 결과코드별 | 테이블: 통합 로그 | 옵션: 일별
	List<Stats> selectUnionLogByResultOpDay(Search search);
	// 통계 결과코드별 | 테이블: 월별 로그 | 옵션: 일별
	List<Stats> selectMonthlyLogByResultOpDay(Search search);

	// 통계 사용자별 | 테이블: 통합 로그 | 옵션: 월별
	List<Stats> selectUnionLogByUserOpMonth(Search search);
	// 통계 사용자별 | 테이블: 월별 로그 | 옵션: 월별
	List<Stats> selectMonthlyLogByUserOpMonth(Search search);
	// 통계 사용자별 | 테이블: 통합 로그 | 옵션: 일별
	List<Stats> selectUnionLogByUserOpDay(Search search);
	// 통계 사용자별 | 테이블: 월별 로그 | 옵션: 일별
	List<Stats> selectMonthlyLogByUserOpDay(Search search);

	// 통계 부서별 | 테이블: 통합 로그 | 옵션: 월별
	List<Stats> selectUnionLogByDeptOpMonth(Search search);
	// 통계 부서별 | 테이블: 월별 로그 | 옵션: 월별
	List<Stats> selectMonthlyLogByDeptOpMonth(Search search);
	// 통계 부서별 | 테이블: 통합 로그 | 옵션: 일별
	List<Stats> selectUnionLogByDeptOpDay(Search search);
	// 통계 부서별 | 테이블: 월별 로그 | 옵션: 일별
	List<Stats> selectMonthlyLogByDeptOpDay(Search search);

	// JSP에서 사용할 새로운 메서드들
	// 업체별 통계 조회 (기존 XML의 selectCompanyStats와 매핑)
	List<Stats> selectCompanyStats(Search search);
	
	// 업체 데이터 조회 메서드 추가
	List<Stats> getCompanyDataList(Search search);
	List<Stats> getAllCompanyData();
	
	// 업체 목록 조회 메서드 추가
	List<Map<String, String>> selectCompanyList();
}