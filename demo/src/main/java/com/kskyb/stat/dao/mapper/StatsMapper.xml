<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kskyb.stat.dao.StatsDao">

    <!-- Stats 객체에 대한 명시적 결과 매핑 - JSP에 맞게 확장 -->
    <resultMap id="statsResultMap" type="com.kskyb.stat.model.Stats">
        <result property="ptnId" column="ptnId" />
        <result property="cName" column="cName" jdbcType="VARCHAR" />  <!-- jdbcType 명시 -->
        <result property="cDate" column="cDate" />
        <result property="statDate" column="cDate" jdbcType="VARCHAR" />  <!-- statDate를 cDate와 같은 값으로 매핑 -->
        <result property="mCodeName" column="mCodeName" />
        <result property="sCount" column="sCount" />
        <result property="fCount" column="fCount" />
        <result property="tCount" column="tCount" />
        
        <!-- 업체 데이터 관리용 필드 추가 -->
        <result property="sdate" column="sdate" />
        <result property="edate" column="edate" />
        
        <!-- SMS 관련 필드들 - JSP가 요구하는 정확한 필드명으로 매핑 -->
        <result property="smsTotal" column="smsTotal" />
        <result property="smsSuccess" column="smsSuccess" />
        <result property="smsFail" column="smsFail" />
        
        <!-- LMS 관련 필드들 - JSP가 요구하는 정확한 필드명으로 매핑 -->
        <result property="lmsTotal" column="lmsTotal" />
        <result property="lmsSuccess" column="lmsSuccess" />
        <result property="lmsFail" column="lmsFail" />
        
        <!-- MMS 관련 필드들 - JSP가 요구하는 정확한 필드명으로 매핑 -->
        <result property="mmsTotal" column="mmsTotal" />
        <result property="mmsSuccess" column="mmsSuccess" />
        <result property="mmsFail" column="mmsFail" />
        
        <!-- RCS-Template 관련 필드들 - JSP가 요구하는 정확한 필드명으로 매핑 -->
        <result property="rcsTemplateTotal" column="rcsTemplateTotal" />
        <result property="rcsTemplateSuccess" column="rcsTemplateSuccess" />
        <result property="rcsTemplateFail" column="rcsTemplateFail" />
        
        <!-- RCS-LMS-Template 관련 필드들 - JSP가 요구하는 정확한 필드명으로 매핑 -->
        <result property="rcsLmsTemplateTotal" column="rcsLmsTemplateTotal" />
        <result property="rcsLmsTemplateSuccess" column="rcsLmsTemplateSuccess" />
        <result property="rcsLmsTemplateFail" column="rcsLmsTemplateFail" />
        
        <!-- 알림톡 관련 필드들 - JSP가 요구하는 정확한 필드명으로 매핑 -->
        <result property="alimtalkTotal" column="alimtalkTotal" />
        <result property="alimtalkSuccess" column="alimtalkSuccess" />
        <result property="alimtalkFail" column="alimtalkFail" />
        
        <!-- 네이버톡 관련 필드들 - JSP가 요구하는 정확한 필드명으로 매핑 -->
        <result property="navertalkTotal" column="navertalkTotal" />
        <result property="navertalkSuccess" column="navertalkSuccess" />
        <result property="navertalkFail" column="navertalkFail" />
    </resultMap>

    <!-- JSP와 일치하는 업체별 통계 조회 쿼리 -->
    <select id="selectCompanyStats" parameterType="com.kskyb.stat.model.Search" resultMap="statsResultMap">
        SELECT 
            COALESCE(kc.C_Name, CONCAT('PTN_ID:', a.PTN_ID)) as cName,
            a.PTN_ID as ptnId,
            a.C_DATE as cDate,
            SUM(a.C_CNT) as tCount,
            SUM(CASE WHEN a.C_RES_CODE = 1000 THEN a.C_CNT ELSE 0 END) as sCount,
            SUM(CASE WHEN a.C_RES_CODE != 1000 THEN a.C_CNT ELSE 0 END) as fCount,
            
            -- M_CODE 이름 매핑 (집계 함수 사용)
            MAX(COALESCE(cc.Common_Name, CAST(a.M_CODE AS CHAR))) as mCodeName,
            
            -- SMS 관련 집계 (M_CODE = 1) - JSP에서 요구하는 Total 필드명 사용
            SUM(CASE WHEN a.M_CODE = 1 THEN a.C_CNT ELSE 0 END) as smsTotal,
            SUM(CASE WHEN a.M_CODE = 1 AND a.C_RES_CODE = 1000 THEN a.C_CNT ELSE 0 END) as smsSuccess,
            SUM(CASE WHEN a.M_CODE = 1 AND a.C_RES_CODE != 1000 THEN a.C_CNT ELSE 0 END) as smsFail,
            
            -- LMS 관련 집계 (M_CODE = 2) - JSP에서 요구하는 Total 필드명 사용
            SUM(CASE WHEN a.M_CODE = 2 THEN a.C_CNT ELSE 0 END) as lmsTotal,
            SUM(CASE WHEN a.M_CODE = 2 AND a.C_RES_CODE = 1000 THEN a.C_CNT ELSE 0 END) as lmsSuccess,
            SUM(CASE WHEN a.M_CODE = 2 AND a.C_RES_CODE != 1000 THEN a.C_CNT ELSE 0 END) as lmsFail,
            
            -- MMS 관련 집계 (M_CODE = 3) - JSP에서 요구하는 Total 필드명 사용
            SUM(CASE WHEN a.M_CODE = 3 THEN a.C_CNT ELSE 0 END) as mmsTotal,
            SUM(CASE WHEN a.M_CODE = 3 AND a.C_RES_CODE = 1000 THEN a.C_CNT ELSE 0 END) as mmsSuccess,
            SUM(CASE WHEN a.M_CODE = 3 AND a.C_RES_CODE != 1000 THEN a.C_CNT ELSE 0 END) as mmsFail,
            
            -- RCS-Template 관련 집계 (M_CODE = 7) - JSP에서 요구하는 Total 필드명 사용
            SUM(CASE WHEN a.M_CODE = 7 THEN a.C_CNT ELSE 0 END) as rcsTemplateTotal,
            SUM(CASE WHEN a.M_CODE = 7 AND a.C_RES_CODE = 1000 THEN a.C_CNT ELSE 0 END) as rcsTemplateSuccess,
            SUM(CASE WHEN a.M_CODE = 7 AND a.C_RES_CODE != 1000 THEN a.C_CNT ELSE 0 END) as rcsTemplateFail,
            
            -- RCS-LMS-Template 관련 집계 (M_CODE = 5, 12를 5로 취급) - JSP에서 요구하는 Total 필드명 사용
            SUM(CASE WHEN a.M_CODE IN (5, 12) THEN a.C_CNT ELSE 0 END) as rcsLmsTemplateTotal,
            SUM(CASE WHEN a.M_CODE IN (5, 12) AND a.C_RES_CODE = 1000 THEN a.C_CNT ELSE 0 END) as rcsLmsTemplateSuccess,
            SUM(CASE WHEN a.M_CODE IN (5, 12) AND a.C_RES_CODE != 1000 THEN a.C_CNT ELSE 0 END) as rcsLmsTemplateFail,
            
            -- 알림톡 관련 집계 (M_CODE = 8) - JSP에서 요구하는 Total 필드명 사용
            SUM(CASE WHEN a.M_CODE = 8 THEN a.C_CNT ELSE 0 END) as alimtalkTotal,
            SUM(CASE WHEN a.M_CODE = 8 AND a.C_RES_CODE = 1000 THEN a.C_CNT ELSE 0 END) as alimtalkSuccess,
            SUM(CASE WHEN a.M_CODE = 8 AND a.C_RES_CODE != 1000 THEN a.C_CNT ELSE 0 END) as alimtalkFail,
            
            -- 네이버톡 관련 집계 (M_CODE = 13) - JSP에서 요구하는 Total 필드명 사용
            SUM(CASE WHEN a.M_CODE = 13 THEN a.C_CNT ELSE 0 END) as navertalkTotal,
            SUM(CASE WHEN a.M_CODE = 13 AND a.C_RES_CODE = 1000 THEN a.C_CNT ELSE 0 END) as navertalkSuccess,
            SUM(CASE WHEN a.M_CODE = 13 AND a.C_RES_CODE != 1000 THEN a.C_CNT ELSE 0 END) as navertalkFail
            
        FROM (
            -- tbl_statistics: 집계 데이터를 건별로 변환 (M_CODE를 숫자로 처리)
            SELECT c_ptnid_int as PTN_ID, c_date_vch as C_DATE, 1 as M_CODE, 1000 as C_RES_CODE, c_totsu_int as C_CNT FROM tbl_statistics WHERE c_totsu_int > 0 AND c_date_vch BETWEEN #{dbSDate} AND #{dbEDate}
            UNION ALL
            SELECT c_ptnid_int as PTN_ID, c_date_vch as C_DATE, 1 as M_CODE, 9999 as C_RES_CODE, (c_total_int - c_totsu_int) as C_CNT FROM tbl_statistics WHERE (c_total_int - c_totsu_int) > 0 AND c_date_vch BETWEEN #{dbSDate} AND #{dbEDate}
            
            UNION ALL
            -- tbl_statistics_bc: 집계 데이터를 건별로 변환 (M_CODE를 숫자로 처리)
            SELECT c_ptnid_int as PTN_ID, c_date_vch as C_DATE, 1 as M_CODE, 1000 as C_RES_CODE, c_totsu_int as C_CNT FROM tbl_statistics_bc WHERE c_totsu_int > 0 AND c_date_vch BETWEEN #{dbSDate} AND #{dbEDate}
            UNION ALL
            SELECT c_ptnid_int as PTN_ID, c_date_vch as C_DATE, 1 as M_CODE, 9999 as C_RES_CODE, (c_total_int - c_totsu_int) as C_CNT FROM tbl_statistics_bc WHERE (c_total_int - c_totsu_int) > 0 AND c_date_vch BETWEEN #{dbSDate} AND #{dbEDate}
            
            UNION ALL
            -- tbl_statistics_bc_search: 집계 데이터를 건별로 변환 (M_CODE를 숫자로 처리)
            SELECT 5 as PTN_ID, c_date_vch as C_DATE, 1 as M_CODE, 1000 as C_RES_CODE, c_totsu_int as C_CNT FROM tbl_statistics_bc_search WHERE c_totsu_int > 0 AND c_date_vch BETWEEN #{dbSDate} AND #{dbEDate}
            UNION ALL
            SELECT 5 as PTN_ID, c_date_vch as C_DATE, 1 as M_CODE, 9999 as C_RES_CODE, (c_total_int - c_totsu_int) as C_CNT FROM tbl_statistics_bc_search WHERE (c_total_int - c_totsu_int) > 0 AND c_date_vch BETWEEN #{dbSDate} AND #{dbEDate}
            
            UNION ALL
            -- tbl_statistics_search: 집계 데이터를 건별로 변환 (M_CODE를 숫자로 처리)
            SELECT c_ptnid_int as PTN_ID, c_date_vch as C_DATE, 1 as M_CODE, 1000 as C_RES_CODE, c_totsu_int as C_CNT FROM tbl_statistics_search WHERE c_totsu_int > 0 AND c_date_vch BETWEEN #{dbSDate} AND #{dbEDate}
            UNION ALL
            SELECT c_ptnid_int as PTN_ID, c_date_vch as C_DATE, 1 as M_CODE, 9999 as C_RES_CODE, (c_total_int - c_totsu_int) as C_CNT FROM tbl_statistics_search WHERE (c_total_int - c_totsu_int) > 0 AND c_date_vch BETWEEN #{dbSDate} AND #{dbEDate}
            
            UNION ALL
            -- tbl_statistics_mms: 실제 건별 데이터 (M_Code 컬럼 사용, PTN_ID 6,7을 5로 변환)
            SELECT CASE WHEN c_ptnid IN (6, 7) THEN 5 ELSE c_ptnid END as PTN_ID, c_date as C_DATE, M_Code as M_CODE, c_res_code as C_RES_CODE, c_cnt as C_CNT FROM tbl_statistics_mms WHERE c_date BETWEEN #{dbSDate} AND #{dbEDate}
            
            UNION ALL
            -- tbl_statistics_mms_search: 실제 건별 데이터 (M_Code 컬럼 사용, PTN_ID 6,7을 5로 변환)
            SELECT CASE WHEN c_ptnid IN (6, 7) THEN 5 ELSE c_ptnid END as PTN_ID, c_date as C_DATE, M_Code as M_CODE, c_res_code as C_RES_CODE, c_cnt as C_CNT FROM tbl_statistics_mms_search WHERE c_date BETWEEN #{dbSDate} AND #{dbEDate}
        ) a
        LEFT JOIN k_customer kc ON a.PTN_ID = kc.PTN_ID
        LEFT JOIN k_common_code cc ON cc.Common_Value = a.M_CODE AND cc.Parent_Idx = 4 AND cc.Del_YN = 'N'
        WHERE 1=1
        
        <if test="ptnId != null and ptnId != ''">
            AND a.PTN_ID = #{ptnId}
        </if>
        
        <choose>
            <when test="cName == null or cName == ''">
                <!-- 업체명이 없으면 모든 업체 조회 -->
            </when>
            <when test="cName == '전체'">
                <!-- 전체일 때는 추가 조건 없음 -->
            </when>
            <otherwise>
                AND kc.C_Name = #{cName} <!-- 특정 업체명 필터링 -->
            </otherwise>
        </choose>
        
        GROUP BY COALESCE(kc.C_Name, CONCAT('PTN_ID:', a.PTN_ID)), a.PTN_ID, a.C_DATE
        ORDER BY a.C_DATE DESC, COALESCE(kc.C_Name, CONCAT('PTN_ID:', a.PTN_ID)) ASC, a.PTN_ID ASC
    </select>

    <!-- 업체 목록 조회 -->
    <select id="selectCompanyList" resultType="java.util.HashMap">
        SELECT DISTINCT C_Name as cName
        FROM k_customer
        WHERE C_Name IS NOT NULL
        ORDER BY C_Name ASC
    </select>

    <!-- 업체 데이터 조회 (검색 조건 포함) - 업체 목록만 조회 -->
    <select id="getCompanyDataList" parameterType="com.kskyb.stat.model.Search" resultMap="statsResultMap">
        SELECT 
            PTN_ID as ptnId,
            C_Name as cName
        FROM k_customer
        WHERE 1=1
        <if test="companyName != null and companyName != ''">
            AND C_Name LIKE CONCAT('%', #{companyName}, '%')
        </if>
        ORDER BY C_Name ASC
    </select>

    <!-- 모든 업체 데이터 조회 - 업체 목록만 조회 -->
    <select id="getAllCompanyData" resultMap="statsResultMap">
        SELECT 
            PTN_ID as ptnId,
            C_Name as cName
        FROM k_customer
        ORDER BY C_Name ASC
    </select>

</mapper>