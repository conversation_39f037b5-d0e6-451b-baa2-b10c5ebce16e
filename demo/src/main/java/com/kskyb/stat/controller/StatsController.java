package com.kskyb.stat.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.kskyb.stat.model.Search;
import com.kskyb.stat.model.Stats;
import com.kskyb.stat.service.StatsService;

@Controller
@RequestMapping("/stats")
public class StatsController {
    @Autowired
    private StatsService statsService;

    /**
     * 모든 요청에서 searchRequest 객체를 기본으로 제공
     */
    @ModelAttribute("searchRequest")
    public Search getDefaultSearchRequest() {
        Search search = new Search();
        search.setSDate("");
        search.setEDate("");
        search.setCName("");
        search.setPeriodType("");
        return search;
    }

    /**
     * 로그인 체크 메서드
     */
    private String checkLogin(HttpSession session, HttpServletRequest request) {
        String userId = (String) session.getAttribute("userId");
        if (userId == null) {
            // 원래 요청한 URL을 세션에 저장 (로그인 후 리다이렉트용)
            String targetUrl = request.getRequestURI();
            if (request.getQueryString() != null) {
                targetUrl += "?" + request.getQueryString();
            }
            session.setAttribute("targetUrl", targetUrl);
            return "redirect:/login";
        }
        return null; // 로그인된 상태
    }

    // JSP 페이지 렌더링
    // 업체 데이터 관리 - 목록 페이지
    @GetMapping("/company")
    public String companyStatsPage(Model model, HttpSession session, HttpServletRequest request) {
        // 로그인 체크
        String loginCheck = checkLogin(session, request);
        if (loginCheck != null) {
            return loginCheck;
        }

        try {
            // 업체 목록만 조회 (검색용 드롭다운에서 사용)
            List<Map<String, String>> companyList = statsService.getCompanyList();
            System.out.println("companyList 크기: " + (companyList != null ? companyList.size() : "null"));
            
            // 초기에는 빈 데이터 리스트
            model.addAttribute("statisticsDataList", new ArrayList<>());
            model.addAttribute("companyList", companyList);
            // searchRequest는 @ModelAttribute로 자동 제공됨
            
        } catch (Exception e) {
            System.err.println("데이터 조회 중 오류 발생: " + e.getMessage());
            e.printStackTrace();
            // 빈 리스트로 초기화
            model.addAttribute("statisticsDataList", new ArrayList<>());
            model.addAttribute("companyList", new ArrayList<>());
        }
        
        return "statistics-company";
    }

    // 업체 데이터 검색
    @PostMapping("/company/search")
    public String searchCompanyData(@ModelAttribute("searchRequest") Search search, Model model, 
                                   HttpSession session, HttpServletRequest request) {
        // 로그인 체크
        String loginCheck = checkLogin(session, request);
        if (loginCheck != null) {
            return loginCheck;
        }

        try {
            System.out.println("검색 조건 - sDate: " + search.getSDate() + ", eDate: " + search.getEDate() + ", cName: " + search.getCName());
            
            // 검색 조건에 따른 통계 데이터 조회
            List<Stats> statisticsDataList = statsService.selectCompanyStats(search);
            
            // 디버깅: Controller에서 JSP로 전달하는 데이터 확인
            System.out.println("=== Controller에서 JSP로 전달하는 데이터 ===");
            System.out.println("statisticsDataList 크기: " + (statisticsDataList != null ? statisticsDataList.size() : "null"));
            if (statisticsDataList != null && !statisticsDataList.isEmpty()) {
                Stats firstStat = statisticsDataList.get(0);
                System.out.println("첫 번째 데이터 확인:");
                System.out.println("  cName: " + firstStat.getCName());
                System.out.println("  ptnId: " + firstStat.getPtnId());
                System.out.println("  statDate: " + firstStat.getStatDate());
                System.out.println("  smsTotal: " + firstStat.getSmsTotal());
                
                // 모든 객체의 cName 확인
                for (int i = 0; i < Math.min(3, statisticsDataList.size()); i++) {
                    Stats stat = statisticsDataList.get(i);
                    System.out.println("  [" + i + "] cName: '" + stat.getCName() + "', class: " + stat.getClass().getName());
                }
            }
            
            // 업체 목록도 다시 조회하여 드롭다운 유지
            List<Map<String, String>> companyList = statsService.getCompanyList();

            // 날짜 형식 변환 (YYYYMMDD -> YYYY-MM-DD) - JSP 표시용
            if (search.getSDate() != null && search.getSDate().length() == 8) {
                String formattedSDate = search.getSDate().substring(0, 4) + "-" + 
                                       search.getSDate().substring(4, 6) + "-" + 
                                       search.getSDate().substring(6, 8);
                search.setSDate(formattedSDate);
            }
            
            if (search.getEDate() != null && search.getEDate().length() == 8) {
                String formattedEDate = search.getEDate().substring(0, 4) + "-" + 
                                       search.getEDate().substring(4, 6) + "-" + 
                                       search.getEDate().substring(6, 8);
                search.setEDate(formattedEDate);
            }

            model.addAttribute("statisticsDataList", statisticsDataList);
            model.addAttribute("companyList", companyList);
            // searchRequest는 @ModelAttribute로 자동 바인딩됨
            
            // 합계 데이터 계산 (필요한 경우)
            if (statisticsDataList != null && !statisticsDataList.isEmpty()) {
                Stats totalStats = calculateTotalStats(statisticsDataList);
                model.addAttribute("totalStats", totalStats);
            }
            
        } catch (Exception e) {
            System.err.println("검색 중 오류 발생: " + e.getMessage());
            e.printStackTrace();
            
            // 오류 발생 시 빈 리스트와 검색 조건 유지
            List<Map<String, String>> companyList = statsService.getCompanyList();
            model.addAttribute("statisticsDataList", new ArrayList<>());
            model.addAttribute("companyList", companyList);
        }

        return "statistics-company";
    }

    // 통계 데이터 합계 계산 메소드
    private Stats calculateTotalStats(List<Stats> statsList) {
        Stats totalStats = new Stats();
        
        int smsTotal = 0, smsSuccess = 0, smsFail = 0;
        int lmsTotal = 0, lmsSuccess = 0, lmsFail = 0;
        int mmsTotal = 0, mmsSuccess = 0, mmsFail = 0;
        int alimtalkTotal = 0, alimtalkSuccess = 0, alimtalkFail = 0;
        int navertalkTotal = 0, navertalkSuccess = 0, navertalkFail = 0;
        
        for (Stats stats : statsList) {
            if (stats.getSmsTotal() != null) smsTotal += stats.getSmsTotal();
            if (stats.getSmsSuccess() != null) smsSuccess += stats.getSmsSuccess();
            if (stats.getSmsFail() != null) smsFail += stats.getSmsFail();
            
            if (stats.getLmsTotal() != null) lmsTotal += stats.getLmsTotal();
            if (stats.getLmsSuccess() != null) lmsSuccess += stats.getLmsSuccess();
            if (stats.getLmsFail() != null) lmsFail += stats.getLmsFail();
            
            if (stats.getMmsTotal() != null) mmsTotal += stats.getMmsTotal();
            if (stats.getMmsSuccess() != null) mmsSuccess += stats.getMmsSuccess();
            if (stats.getMmsFail() != null) mmsFail += stats.getMmsFail();
            
            if (stats.getAlimtalkTotal() != null) alimtalkTotal += stats.getAlimtalkTotal();
            if (stats.getAlimtalkSuccess() != null) alimtalkSuccess += stats.getAlimtalkSuccess();
            if (stats.getAlimtalkFail() != null) alimtalkFail += stats.getAlimtalkFail();
            
            if (stats.getNavertalkTotal() != null) navertalkTotal += stats.getNavertalkTotal();
            if (stats.getNavertalkSuccess() != null) navertalkSuccess += stats.getNavertalkSuccess();
            if (stats.getNavertalkFail() != null) navertalkFail += stats.getNavertalkFail();
        }
        
        totalStats.setSmsTotal(smsTotal);
        totalStats.setSmsSuccess(smsSuccess);
        totalStats.setSmsFail(smsFail);
        totalStats.setLmsTotal(lmsTotal);
        totalStats.setLmsSuccess(lmsSuccess);
        totalStats.setLmsFail(lmsFail);
        totalStats.setMmsTotal(mmsTotal);
        totalStats.setMmsSuccess(mmsSuccess);
        totalStats.setMmsFail(mmsFail);
        totalStats.setAlimtalkTotal(alimtalkTotal);
        totalStats.setAlimtalkSuccess(alimtalkSuccess);
        totalStats.setAlimtalkFail(alimtalkFail);
        totalStats.setNavertalkTotal(navertalkTotal);
        totalStats.setNavertalkSuccess(navertalkSuccess);
        totalStats.setNavertalkFail(navertalkFail);
        
        return totalStats;
    }

    // 업체 데이터 엑셀 다운로드
    @PostMapping("/company/excel")
    public String downloadCompanyExcel(Model model, Search search, HttpSession session, HttpServletRequest request) {
        // 로그인 체크
        String loginCheck = checkLogin(session, request);
        if (loginCheck != null) {
            return loginCheck;
        }

        // 검색 조건에 따른 데이터 조회
        List<Stats> companyDataList = statsService.getCompanyDataList(search);

        model.addAttribute("companyDataList", companyDataList);
        model.addAttribute("searchRequest", search);

        // 엑셀 다운로드 뷰로 이동
        return "excel/companyDataExcel";
    }

    // AJAX 검색 요청 처리
    @PostMapping("/search")
    @ResponseBody
    public ResponseEntity<List<Stats>> searchStats(@RequestBody Search search, HttpSession session) {
        // 로그인 체크 (AJAX용)
        String userId = (String) session.getAttribute("userId");
        if (userId == null) {
            return ResponseEntity.status(401).build(); // Unauthorized
        }

        try {
            List<Stats> statsList = statsService.getCompanyStatsGrouped(search);
            return ResponseEntity.ok(statsList);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    // 엑셀 다운로드
    @PostMapping("/excel")
    public void exportExcel(Search search, HttpServletResponse response, HttpSession session) {
        // 로그인 체크
        String userId = (String) session.getAttribute("userId");
        if (userId == null) {
            try {
                response.sendRedirect("/demo/login");
            } catch (Exception e) {
                e.printStackTrace();
            }
            return;
        }

        try {
            List<Stats> statsList = statsService.getCompanyStatsGrouped(search);

            // 엑셀 파일 생성 및 다운로드 로직
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=statistics_" +
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx");

            // 실제 엑셀 생성 로직은 서비스에서 처리
            statsService.exportToExcel(statsList, response.getOutputStream());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 기존 REST API 유지 (하위 호환성)
    @PostMapping("/company-api")
    @ResponseBody
    public List<Stats> getCompanyStats(@RequestBody Search search) {
        return statsService.getCompanyStats(search);
    }
}