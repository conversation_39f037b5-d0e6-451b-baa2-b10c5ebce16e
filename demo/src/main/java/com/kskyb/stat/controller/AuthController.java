package com.kskyb.stat.controller;

import java.util.HashMap;
import java.util.Map;

import jakarta.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kskyb.stat.model.LoginRequest;
import com.kskyb.stat.model.SmsVerificationRequest;
import com.kskyb.stat.service.LoginService;
import com.kskyb.stat.service.SmsService;

/**
 * SMS 인증 관련 REST API 컨트롤러
 * AJAX 요청을 처리하여 2단계 인증 구현
 */
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private LoginService loginService;
    
    @Autowired
    private SmsService smsService;
    
    /**
     * 비밀번호 검증 및 SMS 발송
     */
    @PostMapping("/verify-and-send-sms")
    public ResponseEntity<Map<String, Object>> verifyPasswordAndSendSms(
            @RequestBody LoginRequest request, HttpSession session) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 1단계: 아이디/비밀번호 검증
            boolean isValid = loginService.validateCredentials(request.getUserId(), request.getPassword());
            
            if (!isValid) {
                response.put("success", false);
                response.put("message", "아이디 또는 비밀번호가 올바르지 않습니다.");
                return ResponseEntity.ok(response);
            }
            
            // 2단계: 등록된 전화번호 조회
            String phoneNumber = loginService.getUserPhoneNumber(request.getUserId());
            if (phoneNumber == null) {
                response.put("success", false);
                response.put("message", "등록된 전화번호를 찾을 수 없습니다. 관리자에게 문의하세요.");
                return ResponseEntity.ok(response);
            }
            
            // 3단계: SMS 인증번호 발송
            boolean smsSent = smsService.sendVerificationCode(phoneNumber, request.getUserId());
            
            if (smsSent) {
                // 세션에 임시 정보 저장
                session.setAttribute("tempUserId", request.getUserId());
                session.setAttribute("phoneNumber", phoneNumber);
                session.setAttribute("smsVerified", false);
                
                response.put("success", true);
                response.put("message", "SMS 인증번호가 발송되었습니다.");
                response.put("phoneNumber", phoneNumber); // 마지막 4자리만 표시용
            } else {
                response.put("success", false);
                response.put("message", "SMS 발송에 실패했습니다. 잠시 후 다시 시도해주세요.");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "서버 오류가 발생했습니다. 관리자에게 문의하세요.");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * SMS 인증번호 재발송
     */
    @PostMapping("/resend-sms")
    public ResponseEntity<Map<String, Object>> resendSms(
            @RequestBody Map<String, String> request, HttpSession session) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String userId = request.get("userId");
            String sessionUserId = (String) session.getAttribute("tempUserId");
            String phoneNumber = (String) session.getAttribute("phoneNumber");
            
            // 세션 검증
            if (!userId.equals(sessionUserId) || phoneNumber == null) {
                response.put("success", false);
                response.put("message", "세션이 만료되었습니다. 다시 시도해주세요.");
                return ResponseEntity.ok(response);
            }
            
            // SMS 재발송
            boolean smsSent = smsService.sendVerificationCode(phoneNumber, userId);
            
            if (smsSent) {
                response.put("success", true);
                response.put("message", "SMS 인증번호가 재발송되었습니다.");
            } else {
                response.put("success", false);
                response.put("message", "SMS 재발송에 실패했습니다. 잠시 후 다시 시도해주세요.");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "서버 오류가 발생했습니다.");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * SMS 인증번호 검증
     */
    @PostMapping("/verify-sms")
    public ResponseEntity<Map<String, Object>> verifySms(
            @RequestBody SmsVerificationRequest request, HttpSession session) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String sessionUserId = (String) session.getAttribute("tempUserId");
            String phoneNumber = (String) session.getAttribute("phoneNumber");
            
            // 세션 검증
            if (!request.getUserId().equals(sessionUserId) || phoneNumber == null) {
                response.put("success", false);
                response.put("message", "세션이 만료되었습니다. 다시 시도해주세요.");
                return ResponseEntity.ok(response);
            }
            
            // SMS 인증번호 검증
            boolean isValid = smsService.verifyCode(phoneNumber, request.getVerificationCode());
            
            if (isValid) {
                // SMS 인증 완료 표시
                session.setAttribute("smsVerified", true);
                
                response.put("success", true);
                response.put("message", "SMS 인증이 완료되었습니다.");
            } else {
                response.put("success", false);
                response.put("message", "인증번호가 올바르지 않거나 만료되었습니다.");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "서버 오류가 발생했습니다.");
        }
        
        return ResponseEntity.ok(response);
    }
}