package com.kskyb.stat.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.kskyb.stat.service.LoginService;

/**
 * 로그인 컨트롤러
 * 사용자 인증 및 세션 관리
 */
@Controller
public class LoginController {
    
    @Autowired
    private LoginService loginService;
    
    /**
     * 로그인 페이지 표시
     */
    @GetMapping("/login")
    public String loginPage(HttpSession session, Model model, 
                           @RequestParam(value = "error", required = false) String error,
                           @RequestParam(value = "logout", required = false) String logout) {
        
        // 이미 로그인된 경우 메인 페이지로 리다이렉트
        if (session.getAttribute("userId") != null) {
            return "redirect:/stats/company";
        }
        
        // 에러 메시지 설정
        if (error != null) {
            model.addAttribute("error", "아이디 또는 비밀번호가 올바르지 않습니다.");
        }
        
        // 로그아웃 메시지 설정
        if (logout != null) {
            model.addAttribute("message", "로그아웃되었습니다.");
        }
        
        return "login";
    }
    
    /**
     * 로그인 처리
     */
    @PostMapping("/login")
    public String login(@RequestParam String userId, 
                       @RequestParam String password,
                       @RequestParam(required = false) String verificationCode,
                       @RequestParam(required = false) boolean rememberMe,
                       HttpSession session,
                       HttpServletRequest request,
                       RedirectAttributes redirectAttributes) {
        
        try {
            // SMS 인증이 완료된 사용자인지 확인
            String sessionUserId = (String) session.getAttribute("tempUserId");
            Boolean smsVerified = (Boolean) session.getAttribute("smsVerified");
            
            if (!userId.equals(sessionUserId) || !Boolean.TRUE.equals(smsVerified)) {
                redirectAttributes.addAttribute("error", "true");
                return "redirect:/login";
            }
            
            // 최종 로그인 처리
            boolean loginSuccess = loginService.authenticateUser(userId, password);
            
            if (loginSuccess) {
                // 세션에 사용자 정보 저장
                session.setAttribute("userId", userId);
                session.setAttribute("loginTime", System.currentTimeMillis());
                
                // 임시 세션 정보 제거
                session.removeAttribute("tempUserId");
                session.removeAttribute("smsVerified");
                session.removeAttribute("phoneNumber");
                
                // Remember Me 처리 (필요시 쿠키 설정)
                if (rememberMe) {
                    // 쿠키 설정 로직 (선택사항)
                }
                
                // 로그인 성공 후 원래 요청했던 페이지 또는 메인 페이지로 이동
                String targetUrl = (String) session.getAttribute("targetUrl");
                session.removeAttribute("targetUrl");
                
                return "redirect:" + (targetUrl != null ? targetUrl : "/stats/company");
            } else {
                redirectAttributes.addAttribute("error", "true");
                return "redirect:/login";
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            redirectAttributes.addAttribute("error", "true");
            return "redirect:/login";
        }
    }
    
    /**
     * 로그아웃 처리
     */
    @GetMapping("/logout")
    public String logout(HttpSession session, RedirectAttributes redirectAttributes) {
        // 세션 무효화
        session.invalidate();
        
        redirectAttributes.addAttribute("logout", "true");
        return "redirect:/login";
    }
    
    /**
     * 메인 페이지 (로그인 후 기본 페이지)
     */
    @GetMapping("/")
    public String home(HttpSession session) {
        // 로그인 확인
        if (session.getAttribute("userId") == null) {
            return "redirect:/login";
        }
        
        return "redirect:/stats/company";
    }
}