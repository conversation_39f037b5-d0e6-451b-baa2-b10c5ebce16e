package com.kskyb.stat.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 테스트용 컨트롤러 - 서버가 정상 작동하는지 확인
 */
@Controller
public class TestController {
    
    @GetMapping("/test")
    @ResponseBody
    public String test() {
        return "Spring MVC is working! Current time: " + new java.util.Date();
    }
    
    @GetMapping("/health")
    @ResponseBody
    public String health() {
        return "OK - Server is running properly";
    }
}