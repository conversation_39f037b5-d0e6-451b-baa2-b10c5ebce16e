package com.kskyb.stat.controller;

import com.kskyb.stat.dao.StatsDao;
import com.kskyb.stat.model.Search;
import com.kskyb.stat.model.Stats;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import java.util.List;

/**
 * 테스트용 컨트롤러 - 서버가 정상 작동하는지 확인
 */
@Controller
public class TestController {
    
    @Autowired
    private StatsDao statsDao;
    
    @GetMapping("/test")
    @ResponseBody
    public String test() {
        return "Spring MVC is working! Current time: " + new java.util.Date();
    }
    
    @GetMapping("/health")
    @ResponseBody
    public String health() {
        return "OK - Server is running properly";
    }
    
    @GetMapping("/test-query")
    @ResponseBody
    public String testCompanyStatsQuery() {
        try {
            // 테스트용 검색 조건 설정
            Search search = new Search();
            search.setSDate("2024-01-01");  // 시작일
            search.setEDate("2024-12-31");  // 종료일
            
            // 쿼리 실행
            List<Stats> results = statsDao.selectCompanyStats(search);
            
            StringBuilder sb = new StringBuilder();
            sb.append("쿼리 테스트 결과:\n");
            sb.append("총 조회 건수: ").append(results.size()).append("\n\n");
            
            // 결과 데이터 출력 (최대 10건)
            int count = 0;
            for (Stats stat : results) {
                if (count >= 10) {
                    sb.append("... (총 ").append(results.size()).append("건 중 10건만 표시)\n");
                    break;
                }
                
                sb.append("=== 결과 ").append(++count).append(" ===\n");
                sb.append("업체명: ").append(stat.getCName()).append("\n");
                sb.append("파트너ID: ").append(stat.getPtnId()).append("\n");
                sb.append("날짜: ").append(stat.getCDate()).append("\n");
                sb.append("전체건수: ").append(stat.getTCount()).append("\n");
                sb.append("성공건수: ").append(stat.getSCount()).append("\n");
                sb.append("실패건수: ").append(stat.getFCount()).append("\n");
                sb.append("SMS 전체: ").append(stat.getSmsTotal()).append("\n");
                sb.append("SMS 성공: ").append(stat.getSmsSuccess()).append("\n");
                sb.append("SMS 실패: ").append(stat.getSmsFail()).append("\n");
                sb.append("LMS 전체: ").append(stat.getLmsTotal()).append("\n");
                sb.append("MMS 전체: ").append(stat.getMmsTotal()).append("\n");
                sb.append("알림톡 전체: ").append(stat.getAlimtalkTotal()).append("\n");
                sb.append("\n");
            }
            
            return sb.toString().replace("\n", "<br>");
            
        } catch (Exception e) {
            return "쿼리 테스트 실패: " + e.getMessage() + "<br>스택트레이스: " + getStackTrace(e);
        }
    }
    
    private String getStackTrace(Exception e) {
        StringBuilder sb = new StringBuilder();
        for (StackTraceElement element : e.getStackTrace()) {
            sb.append(element.toString()).append("<br>");
        }
        return sb.toString();
    }
}