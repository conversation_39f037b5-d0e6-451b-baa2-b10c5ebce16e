package com.kskyb.stat.service;

import com.kskyb.stat.model.User;

/**
 * 로그인 서비스 인터페이스
 */
public interface LoginService {
    
    /**
     * 사용자 인증 (아이디/비밀번호 검증)
     * @param userId 사용자 아이디
     * @param password 비밀번호
     * @return 인증 성공 여부
     */
    boolean authenticateUser(String userId, String password);
    
    /**
     * 사용자 자격 증명 검증 (비밀번호만 검증)
     * @param userId 사용자 아이디
     * @param password 비밀번호
     * @return 검증 성공 여부
     */
    boolean validateCredentials(String userId, String password);
    
    /**
     * 사용자 정보 조회
     * @param userId 사용자 아이디
     * @return 사용자 정보
     */
    User getUserInfo(String userId);
    
    /**
     * 사용자 전화번호 조회
     * @param userId 사용자 아이디
     * @return 등록된 전화번호
     */
    String getUserPhoneNumber(String userId);
    
    /**
     * 마지막 로그인 시간 업데이트
     * @param userId 사용자 아이디
     */
    void updateLastLoginTime(String userId);
    
    /**
     * 사용자 활성 상태 확인
     * @param userId 사용자 아이디
     * @return 활성 상태 여부
     */
    boolean isUserActive(String userId);
}