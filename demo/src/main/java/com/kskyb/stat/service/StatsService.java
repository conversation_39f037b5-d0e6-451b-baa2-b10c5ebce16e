package com.kskyb.stat.service;

import com.kskyb.stat.model.Search;
import com.kskyb.stat.model.Stats;
import java.util.List;
import java.util.Map;
import java.io.OutputStream;

public interface StatsService {
    List<Stats> getCompanyStats(Search search);
    
    // 업체별 통계 조회 (매퍼의 selectCompanyStats와 연결)
    List<Stats> selectCompanyStats(Search search);
    
    // 업체 목록 조회
    List<Map<String, String>> getCompanyList();
    
    // 메시지 타입별로 그룹화된 통계 조회
    List<Stats> getCompanyStatsGrouped(Search search);
    
    // 엑셀 내보내기
    void exportToExcel(List<Stats> statsList, OutputStream outputStream) throws Exception;
    
    // 업체 데이터 관리용 메서드 추가
    List<Stats> getCompanyDataList(Search search);
    List<Stats> getAllCompanyData();
}