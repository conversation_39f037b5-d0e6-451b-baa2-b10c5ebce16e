package com.kskyb.stat.service;

/**
 * SMS 인증 서비스 인터페이스
 */
public interface SmsService {
    
    /**
     * SMS 인증번호 발송
     * @param phoneNumber 수신 전화번호
     * @param userId 사용자 아이디 (로깅용)
     * @return 발송 성공 여부
     */
    boolean sendVerificationCode(String phoneNumber, String userId);
    
    /**
     * SMS 인증번호 검증
     * @param phoneNumber 전화번호
     * @param verificationCode 입력된 인증번호
     * @return 인증 성공 여부
     */
    boolean verifyCode(String phoneNumber, String verificationCode);
    
    /**
     * 인증번호 만료 처리
     * @param phoneNumber 전화번호
     */
    void expireCode(String phoneNumber);
    
    /**
     * 인증번호 생성
     * @return 6자리 랜덤 인증번호
     */
    String generateVerificationCode();
}