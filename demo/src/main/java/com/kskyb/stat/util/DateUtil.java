package com.kskyb.stat.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 날짜 변환 유틸리티 클래스
 * 화면에서 입력받는 YYYY-MM-DD 형식을 데이터베이스용 YYYYMMDD 형식으로 변환
 */
public class DateUtil {
    
    // 화면 입력 형식 (YYYY-MM-DD)
    private static final DateTimeFormatter DISPLAY_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    // 데이터베이스 저장 형식 (YYYYMMDD)
    private static final DateTimeFormatter DB_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    /**
     * 화면 날짜 형식(YYYY-MM-DD)을 데이터베이스 형식(YYYYMMDD)으로 변환
     * @param displayDate 화면 날짜 (예: "2025-07-01")
     * @return 데이터베이스 날짜 (예: "20250701")
     */
    public static String convertToDbFormat(String displayDate) {
        if (displayDate == null || displayDate.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 하이픈이 있는 경우만 변환 처리
            if (displayDate.contains("-")) {
                LocalDate date = LocalDate.parse(displayDate, DISPLAY_FORMAT);
                return date.format(DB_FORMAT);
            }
            
            // 이미 YYYYMMDD 형식인 경우 그대로 반환
            return displayDate;
            
        } catch (Exception e) {
            // 변환 실패 시 원본 반환 (로그 출력 후)
            System.err.println("날짜 형식 변환 실패: " + displayDate + ", 오류: " + e.getMessage());
            return displayDate;
        }
    }
    
    /**
     * 데이터베이스 날짜 형식(YYYYMMDD)을 화면 형식(YYYY-MM-DD)으로 변환
     * @param dbDate 데이터베이스 날짜 (예: "20250701")
     * @return 화면 날짜 (예: "2025-07-01")
     */
    public static String convertToDisplayFormat(String dbDate) {
        if (dbDate == null || dbDate.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 8자리 숫자 형식인 경우만 변환
            if (dbDate.length() == 8 && dbDate.matches("\\d{8}")) {
                LocalDate date = LocalDate.parse(dbDate, DB_FORMAT);
                return date.format(DISPLAY_FORMAT);
            }
            
            // 이미 YYYY-MM-DD 형식인 경우 그대로 반환
            return dbDate;
            
        } catch (Exception e) {
            // 변환 실패 시 원본 반환 (로그 출력 후)
            System.err.println("날짜 형식 변환 실패: " + dbDate + ", 오류: " + e.getMessage());
            return dbDate;
        }
    }
    
    /**
     * 현재 날짜를 화면 형식(YYYY-MM-DD)으로 반환
     * @return 현재 날짜 (예: "2025-08-25")
     */
    public static String getCurrentDateForDisplay() {
        return LocalDate.now().format(DISPLAY_FORMAT);
    }
    
    /**
     * 현재 날짜를 데이터베이스 형식(YYYYMMDD)으로 반환
     * @return 현재 날짜 (예: "20250825")
     */
    public static String getCurrentDateForDb() {
        return LocalDate.now().format(DB_FORMAT);
    }
    
    /**
     * 날짜 형식이 유효한지 검증 (YYYY-MM-DD)
     * @param dateString 검증할 날짜 문자열
     * @return 유효하면 true, 그렇지 않으면 false
     */
    public static boolean isValidDisplayDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return false;
        }
        
        try {
            LocalDate.parse(dateString, DISPLAY_FORMAT);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 날짜 형식이 유효한지 검증 (YYYYMMDD)
     * @param dateString 검증할 날짜 문자열
     * @return 유효하면 true, 그렇지 않으면 false
     */
    public static boolean isValidDbDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return false;
        }
        
        try {
            LocalDate.parse(dateString, DB_FORMAT);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}