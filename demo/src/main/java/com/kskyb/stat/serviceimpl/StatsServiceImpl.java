package com.kskyb.stat.serviceimpl;

import com.kskyb.stat.model.Search;
import com.kskyb.stat.model.Stats;
import com.kskyb.stat.dao.StatsDao;
import com.kskyb.stat.service.StatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;

@Service
public class StatsServiceImpl implements StatsService {
    @Autowired
    private StatsDao statsDao;

    @Override
    public List<Stats> getCompanyStats(Search search) {
        return statsDao.selectUnionLogOpMonth(search);
    }
    
    @Override
    public List<Stats> selectCompanyStats(Search search) {
        try {
            System.out.println("=== 검색 조건 ===");
            System.out.println("sDate: " + search.getSDate());
            System.out.println("eDate: " + search.getEDate());
            System.out.println("cName: " + search.getCName());
            System.out.println("ptnId: " + search.getPtnId());
            
            List<Stats> stats = statsDao.selectCompanyStats(search);
            System.out.println("=== 조회된 데이터 ===");
            System.out.println("총 레코드 수: " + stats.size());
            
            // 디버깅: null인 cName 확인 및 statDate 설정
            int nullCount = 0;
            for (Stats stat : stats) {
                System.out.println("PTN_ID: " + stat.getPtnId() + ", cName: '" + stat.getCName() + "', cDate: " + stat.getCDate());
                
                // 추가 디버깅: 객체 상태 확인
                try {
                    System.out.println("smsTotal: " + stat.getSmsTotal());
                    System.out.println("객체 상태: 정상");
                } catch (Exception e) {
                    System.out.println("⚠️ 객체 상태: 이상 - " + e.getMessage());
                }
                
                // cName null 체크
                if (stat.getCName() == null || stat.getCName().trim().isEmpty()) {
                    System.out.println("⚠️ WARNING: PTN_ID " + stat.getPtnId() + "의 cName이 null입니다!");
                    stat.setCName("PTN_ID:" + stat.getPtnId());
                    nullCount++;
                }
                
                // statDate가 null이면 cDate 값으로 설정
                if (stat.getStatDate() == null && stat.getCDate() != null) {
                    stat.setStatDate(stat.getCDate());
                }
            }
            
            if (nullCount > 0) {
                System.out.println("⚠️ 총 " + nullCount + "개의 레코드에서 cName이 null이었습니다.");
            } else {
                System.out.println("✅ 모든 레코드의 cName이 정상입니다.");
            }
            
            return stats;
        } catch (Exception e) {
            System.out.println("❌ 오류 발생: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, String>> getCompanyList() {
        try {
            return statsDao.selectCompanyList();
        } catch (Exception e) {
            // 데이터베이스 조회 실패 시 빈 리스트 반환 또는 로그 출력
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Stats> getCompanyStatsGrouped(Search search) {
        try {
            // 기존 쿼리에서 데이터를 가져와서 메시지 타입별로 그룹화
            List<Stats> rawStats = statsDao.selectCompanyStats(search);
            return groupStatsByMessageType(rawStats);
        } catch (Exception e) {
            // 데이터베이스 조회 실패 시 빈 리스트 반환
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    // 메시지 타입별로 통계 데이터 그룹화
    private List<Stats> groupStatsByMessageType(List<Stats> rawStats) {
        Map<String, Stats> groupedStats = new HashMap<>();
        
        for (Stats stat : rawStats) {
            // cName이 null인 경우 안전 장치 추가
            String cName = stat.getCName();
            if (cName == null || cName.trim().isEmpty()) {
                cName = "PTN_ID:" + stat.getPtnId();
                stat.setCName(cName); // 원본 객체에도 설정
            }
            
            String key = cName + "_" + stat.getPtnId() + "_" + stat.getCDate();
            
            Stats groupedStat = groupedStats.get(key);
            if (groupedStat == null) {
                groupedStat = new Stats();
                groupedStat.setCName(cName); // 안전한 cName 사용
                groupedStat.setPtnId(stat.getPtnId());
                groupedStat.setCDate(stat.getCDate());
                groupedStats.put(key, groupedStat);
            }
            
            // M_CODE_NAME에 따라 해당 메시지 타입 필드에 값 설정
            String mCodeName = stat.getMCodeName();
            if (mCodeName != null) {
                switch (mCodeName.toLowerCase()) {
                    case "sms":
                        groupedStat.setSmsSent(stat.getSCount() + stat.getFCount());
                        groupedStat.setSmsSuccess(stat.getSCount());
                        groupedStat.setSmsFail(stat.getFCount());
                        break;
                    case "lms":
                        groupedStat.setLmsSent(stat.getSCount() + stat.getFCount());
                        groupedStat.setLmsSuccess(stat.getSCount());
                        groupedStat.setLmsFail(stat.getFCount());
                        break;
                    case "mms":
                        groupedStat.setMmsSent(stat.getSCount() + stat.getFCount());
                        groupedStat.setMmsSuccess(stat.getSCount());
                        groupedStat.setMmsFail(stat.getFCount());
                        break;
                    case "알림톡":
                        groupedStat.setAlimtalkSent(stat.getSCount() + stat.getFCount());
                        groupedStat.setAlimtalkSuccess(stat.getSCount());
                        groupedStat.setAlimtalkFail(stat.getFCount());
                        break;
                    case "네이버톡":
                        groupedStat.setNavertalkSent(stat.getSCount() + stat.getFCount());
                        groupedStat.setNavertalkSuccess(stat.getSCount());
                        groupedStat.setNavertalkFail(stat.getFCount());
                        break;
                }
            }
        }
        
        return new ArrayList<>(groupedStats.values());
    }
    
    @Override
    public void exportToExcel(List<Stats> statsList, OutputStream outputStream) throws Exception {
        // 간단한 CSV 형태로 내보내기 (실제로는 Apache POI 사용 권장)
        try (OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8)) {
            // UTF-8 BOM 추가 (Excel에서 한글 깨짐 방지)
            writer.write('\uFEFF');
            
            // 헤더 작성
            writer.write("업체명,파트너ID,날짜,SMS발송,SMS성공,SMS실패,LMS발송,LMS성공,LMS실패,MMS발송,MMS성공,MMS실패,알림톡발송,알림톡성공,알림톡실패,네이버톡발송,네이버톡성공,네이버톡실패\n");
            
            // 데이터 작성
            for (Stats stat : statsList) {
                writer.write(String.format("%s,%s,%s,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d\n",
                    stat.getCName(), stat.getPtnId(), stat.getCDate(),
                    stat.getSmsSent(), stat.getSmsSuccess(), stat.getSmsFail(),
                    stat.getLmsSent(), stat.getLmsSuccess(), stat.getLmsFail(),
                    stat.getMmsSent(), stat.getMmsSuccess(), stat.getMmsFail(),
                    stat.getAlimtalkSent(), stat.getAlimtalkSuccess(), stat.getAlimtalkFail(),
                    stat.getNavertalkSent(), stat.getNavertalkSuccess(), stat.getNavertalkFail()));
            }
        }
    }
    
    @Override
    public List<Stats> getCompanyDataList(Search search) {
        return statsDao.getCompanyDataList(search);
    }
    
    @Override
    public List<Stats> getAllCompanyData() {
        return statsDao.getAllCompanyData();
    }
}