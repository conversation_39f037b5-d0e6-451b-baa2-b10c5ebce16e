package com.kskyb.stat.serviceimpl;

import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.kskyb.stat.model.User;
import com.kskyb.stat.service.LoginService;

/**
 * 로그인 서비스 구현체
 * 실제 환경에서는 데이터베이스와 연동하여 사용자 정보를 관리
 */
@Service
public class LoginServiceImpl implements LoginService {
    
    // 개발용 테스트 사용자 데이터 (실제 환경에서는 데이터베이스 사용)
    private static final Map<String, User> TEST_USERS = new HashMap<>();
    
    static {
        // 테스트용 사용자 데이터 초기화
        User adminUser = new User();
        adminUser.setUserId("admin");
        adminUser.setPassword(hashPassword("admin123")); // SHA-256 해싱
        adminUser.setUserName("관리자");
        adminUser.setPhoneNumber("010-1234-5678");
        adminUser.setEmail("<EMAIL>");
        adminUser.setDepartment("IT팀");
        adminUser.setRole("ADMIN");
        adminUser.setActive(true);
        
        TEST_USERS.put("admin", adminUser);
        
        // 추가 테스트 사용자
        User testUser = new User();
        testUser.setUserId("test");
        testUser.setPassword(hashPassword("test123"));
        testUser.setUserName("테스트 사용자");
        testUser.setPhoneNumber("010-9876-5432");
        testUser.setEmail("<EMAIL>");
        testUser.setDepartment("마케팅팀");
        testUser.setRole("USER");
        testUser.setActive(true);
        
        TEST_USERS.put("test", testUser);
    }
    
    @Override
    public boolean authenticateUser(String userId, String password) {
        try {
            User user = TEST_USERS.get(userId);
            if (user == null || !user.isActive()) {
                return false;
            }
            
            String hashedPassword = hashPassword(password);
            boolean isAuthenticated = user.getPassword().equals(hashedPassword);
            
            if (isAuthenticated) {
                updateLastLoginTime(userId);
            }
            
            return isAuthenticated;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean validateCredentials(String userId, String password) {
        try {
            User user = TEST_USERS.get(userId);
            if (user == null || !user.isActive()) {
                return false;
            }
            
            String hashedPassword = hashPassword(password);
            return user.getPassword().equals(hashedPassword);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public User getUserInfo(String userId) {
        return TEST_USERS.get(userId);
    }
    
    @Override
    public String getUserPhoneNumber(String userId) {
        User user = TEST_USERS.get(userId);
        return user != null ? user.getPhoneNumber() : null;
    }
    
    @Override
    public void updateLastLoginTime(String userId) {
        User user = TEST_USERS.get(userId);
        if (user != null) {
            user.setLastLoginDate(new java.util.Date());
        }
    }
    
    @Override
    public boolean isUserActive(String userId) {
        User user = TEST_USERS.get(userId);
        return user != null && user.isActive();
    }
    
    /**
     * 비밀번호 해싱 (SHA-256)
     * 실제 환경에서는 BCrypt 등 더 안전한 해싱 알고리즘 사용 권장
     */
    private static String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("패스워드 해싱 중 오류 발생", e);
        }
    }
}