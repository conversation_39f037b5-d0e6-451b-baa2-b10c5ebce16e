package com.kskyb.stat.serviceimpl;

import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Service;

import com.kskyb.stat.service.SmsService;

/**
 * SMS 인증 서비스 구현체
 * 실제 환경에서는 SMS 게이트웨이 API와 연동
 */
@Service
public class SmsServiceImpl implements SmsService {
    
    // 인증번호 저장소 (실제 환경에서는 Redis 등 캐시 서버 사용 권장)
    private final Map<String, VerificationInfo> verificationCodes = new ConcurrentHashMap<>();
    
    // 인증번호 만료 처리용 스케줄러
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    // 인증번호 유효 시간 (3분)
    private static final long EXPIRATION_TIME = 3 * 60 * 1000; // 3분
    
    @Override
    public boolean sendVerificationCode(String phoneNumber, String userId) {
        try {
            // 인증번호 생성
            String code = generateVerificationCode();
            
            // 인증 정보 저장
            VerificationInfo info = new VerificationInfo(code, System.currentTimeMillis(), userId);
            verificationCodes.put(phoneNumber, info);
            
            // 실제 SMS 발송 (개발 환경에서는 로그로 대체)
            boolean smsSent = sendSmsToProvider(phoneNumber, code);
            
            if (smsSent) {
                // 3분 후 자동 만료 스케줄링
                scheduler.schedule(() -> {
                    VerificationInfo currentInfo = verificationCodes.get(phoneNumber);
                    if (currentInfo != null && currentInfo.getCode().equals(code)) {
                        verificationCodes.remove(phoneNumber);
                        System.out.println("인증번호 만료: " + phoneNumber + " (사용자: " + userId + ")");
                    }
                }, EXPIRATION_TIME, TimeUnit.MILLISECONDS);
                
                System.out.println("SMS 발송 성공: " + phoneNumber + " -> " + code + " (사용자: " + userId + ")");
            }
            
            return smsSent;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean verifyCode(String phoneNumber, String verificationCode) {
        try {
            VerificationInfo info = verificationCodes.get(phoneNumber);
            
            if (info == null) {
                System.out.println("인증번호 없음: " + phoneNumber);
                return false;
            }
            
            // 만료 시간 체크
            long currentTime = System.currentTimeMillis();
            if (currentTime - info.getCreatedTime() > EXPIRATION_TIME) {
                verificationCodes.remove(phoneNumber);
                System.out.println("인증번호 만료: " + phoneNumber);
                return false;
            }
            
            // 인증번호 확인
            boolean isValid = info.getCode().equals(verificationCode);
            
            if (isValid) {
                // 인증 성공 시 코드 제거
                verificationCodes.remove(phoneNumber);
                System.out.println("SMS 인증 성공: " + phoneNumber + " (사용자: " + info.getUserId() + ")");
            } else {
                System.out.println("SMS 인증 실패: " + phoneNumber + " - 잘못된 코드");
            }
            
            return isValid;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public void expireCode(String phoneNumber) {
        verificationCodes.remove(phoneNumber);
    }
    
    @Override
    public String generateVerificationCode() {
        Random random = new Random();
        int code = 100000 + random.nextInt(900000); // 6자리 숫자
        return String.valueOf(code);
    }
    
    /**
     * SMS 발송 (실제 SMS 게이트웨이 연동)
     * 개발 환경에서는 테스트용 코드 사용
     */
    private boolean sendSmsToProvider(String phoneNumber, String code) {
        try {
            // 개발 환경에서는 테스트용 코드 "123456" 허용
            if ("123456".equals(code) || phoneNumber.endsWith("5678")) {
                // 테스트 환경에서는 항상 성공
                System.out.println("=== SMS 발송 (개발용) ===");
                System.out.println("수신번호: " + phoneNumber);
                System.out.println("인증번호: " + code);
                System.out.println("메시지: [KSKYB] 인증번호는 " + code + "입니다. 3분 내에 입력해주세요.");
                System.out.println("========================");
                return true;
            }
            
            // 실제 환경에서는 SMS 게이트웨이 API 호출
            // 예: 네이버 클라우드, AWS SNS, 카카오 알림톡 등
            /*
            RestTemplate restTemplate = new RestTemplate();
            
            Map<String, Object> smsRequest = new HashMap<>();
            smsRequest.put("to", phoneNumber);
            smsRequest.put("message", "[KSKYB] 인증번호는 " + code + "입니다. 3분 내에 입력해주세요.");
            smsRequest.put("from", "15441234"); // 발신번호
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer YOUR_API_KEY");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(smsRequest, headers);
            ResponseEntity<String> response = restTemplate.postForEntity("SMS_GATEWAY_URL", entity, String.class);
            
            return response.getStatusCode().is2xxSuccessful();
            */
            
            // 임시로 성공 반환 (개발용)
            return true;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 인증번호 정보 클래스
     */
    private static class VerificationInfo {
        private final String code;
        private final long createdTime;
        private final String userId;
        
        public VerificationInfo(String code, long createdTime, String userId) {
            this.code = code;
            this.createdTime = createdTime;
            this.userId = userId;
        }
        
        public String getCode() {
            return code;
        }
        
        public long getCreatedTime() {
            return createdTime;
        }
        
        public String getUserId() {
            return userId;
        }
    }
}