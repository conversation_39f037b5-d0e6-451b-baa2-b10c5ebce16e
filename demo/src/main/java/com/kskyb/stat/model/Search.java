package com.kskyb.stat.model;

import com.kskyb.stat.util.DateUtil;

public class Search {
    // 기본 검색 조건 필드들
    private int periodKind;
    private String sDate;        // 시작일 (YYYYMMDD)
    private String eDate;        // 종료일 (YYYYMMDD)
    private String cName;        // 업체명
    private String ptnId;        // 파트너 ID
    private String periodType;   // 조회 단위
    
    // 기존 호환성을 위한 필드들
    private String pYearMonth;
    private String pYearMonth2;
    private String ptnIds;
    
    // 기본 생성자
    public Search() {}
    
    // getter/setter - 중복 제거하고 단순화
    public int getPeriodKind() { return periodKind; }
    public void setPeriodKind(int periodKind) { this.periodKind = periodKind; }
    
    public String getSDate() { return sDate; }
    public void setSDate(String sDate) { this.sDate = sDate; }
    
    public String getEDate() { return eDate; }
    public void setEDate(String eDate) { this.eDate = eDate; }
    
    public String getCName() { return cName; }
    public void setCName(String cName) { this.cName = cName; }
    
    public String getPtnId() { return ptnId; }
    public void setPtnId(String ptnId) { this.ptnId = ptnId; }
    
    public String getPeriodType() { return periodType; }
    public void setPeriodType(String periodType) { this.periodType = periodType; }
    
    // 기존 호환성을 위한 getter/setter
    public String getPYearMonth() { return pYearMonth; }
    public void setPYearMonth(String pYearMonth) { this.pYearMonth = pYearMonth; }
    
    public String getPYearMonth2() { return pYearMonth2; }
    public void setPYearMonth2(String pYearMonth2) { this.pYearMonth2 = pYearMonth2; }
    
    public String getPtnIds() { return ptnIds; }
    public void setPtnIds(String ptnIds) { this.ptnIds = ptnIds; }
    
    // 호환성을 위한 별칭 getter/setter (중복 필드 제거)
    public String getStartDate() { return sDate; }
    public void setStartDate(String startDate) { this.sDate = startDate; }
    
    public String getEndDate() { return eDate; }
    public void setEndDate(String endDate) { this.eDate = endDate; }
    
    public String getCompanyName() { return cName; }
    public void setCompanyName(String companyName) { this.cName = companyName; }
    
    /**
     * 화면에서 입력받은 날짜를 데이터베이스 형식으로 변환하여 반환
     * @return 데이터베이스용 시작일 (YYYYMMDD)
     */
    public String getDbSDate() {
        return DateUtil.convertToDbFormat(this.sDate);
    }
    
    /**
     * 화면에서 입력받은 날짜를 데이터베이스 형식으로 변환하여 반환
     * @return 데이터베이스용 종료일 (YYYYMMDD)
     */
    public String getDbEDate() {
        return DateUtil.convertToDbFormat(this.eDate);
    }
    
    /**
     * 데이터베이스 형식 날짜를 화면 표시용으로 변환하여 설정
     * @param dbDate 데이터베이스 형식 날짜 (YYYYMMDD)
     */
    public void setDisplaySDate(String dbDate) {
        this.sDate = DateUtil.convertToDisplayFormat(dbDate);
    }
    
    /**
     * 데이터베이스 형식 날짜를 화면 표시용으로 변환하여 설정
     * @param dbDate 데이터베이스 형식 날짜 (YYYYMMDD)
     */
    public void setDisplayEDate(String dbDate) {
        this.eDate = DateUtil.convertToDisplayFormat(dbDate);
    }
}