package com.kskyb.stat.model;

/**
 * 통계 데이터 모델 클래스
 * 업체별, 날짜별 메시지 발송 통계 정보를 담는 DTO
 */
public class Stats {
    private String ptnId;
    private String cName; // 회사명 필드 추가
    private String statDate; // 통계 날짜
    private String cDate; // ServiceImpl에서 사용하는 날짜 필드 추가
    private String mCodeName; // M_CODE 이름 (SMS, LMS, MMS, 알림톡, 네이버톡)
    private int sCount;
    private int fCount;
    private int tCount;
    
    // 업체 데이터 관리용 필드 추가
    private String sdate;  // 시작일
    private String edate;  // 종료일
    
    // SMS 관련 필드
    private Integer smsTotal = 0;
    private Integer smsSuccess = 0;
    private Integer smsFail = 0;
    private Integer smsSent = 0; // ServiceImpl에서 사용하는 필드 추가
    
    // LMS 관련 필드
    private Integer lmsTotal = 0;
    private Integer lmsSuccess = 0;
    private Integer lmsFail = 0;
    private Integer lmsSent = 0; // ServiceImpl에서 사용하는 필드 추가
    
    // MMS 관련 필드
    private Integer mmsTotal = 0;
    private Integer mmsSuccess = 0;
    private Integer mmsFail = 0;
    private Integer mmsSent = 0; // ServiceImpl에서 사용하는 필드 추가
    
    // RCS-Template 관련 필드 추가
    private Integer rcsTemplateTotal = 0;
    private Integer rcsTemplateSuccess = 0;
    private Integer rcsTemplateFail = 0;
    private Integer rcsTemplateSent = 0;
    
    // RCS-LMS-Template 관련 필드 추가
    private Integer rcsLmsTemplateTotal = 0;
    private Integer rcsLmsTemplateSuccess = 0;
    private Integer rcsLmsTemplateFail = 0;
    private Integer rcsLmsTemplateSent = 0;
    
    // 알림톡 관련 필드
    private Integer alimtalkTotal = 0;
    private Integer alimtalkSuccess = 0;
    private Integer alimtalkFail = 0;
    private Integer alimtalkSent = 0; // ServiceImpl에서 사용하는 필드 추가
    
    // 네이버톡 관련 필드
    private Integer navertalkTotal = 0;
    private Integer navertalkSuccess = 0;
    private Integer navertalkFail = 0;
    private Integer navertalkSent = 0; // ServiceImpl에서 사용하는 필드 추가
    
    // 기본 생성자
    public Stats() {}
    
    // 생성자
    public Stats(String cName, String ptnId, String sDate, String eDate) {
        this.cName = cName;
        this.ptnId = ptnId;
        this.sdate = sDate;
        this.edate = eDate;
    }
    
    // 기본 필드 getter/setter
    public String getPtnId() { return ptnId; }
    public void setPtnId(String ptnId) { this.ptnId = ptnId; }
    
    public String getCName() { return cName; }
    public void setCName(String cName) { this.cName = cName; }
    
    // JSP 테스트용 메서드 추가
    public String getTestCName() { 
        System.out.println("getTestCName() 호출됨: " + this.cName);
        return this.cName; 
    }
    
    public String getStatDate() { return statDate; }
    public void setStatDate(String statDate) { this.statDate = statDate; }
    
    public String getCDate() { return cDate; }
    public void setCDate(String cDate) { this.cDate = cDate; }
    
    // JSP 테스트용 메서드 추가
    public String getTestCDate() { 
        System.out.println("getTestCDate() 호출됨: " + this.cDate);
        return this.cDate; 
    }
    
    public String getMCodeName() { return mCodeName; }
    public void setMCodeName(String mCodeName) { this.mCodeName = mCodeName; }
    
    public int getSCount() { return sCount; }
    public void setSCount(int sCount) { this.sCount = sCount; }
    
    public int getFCount() { return fCount; }
    public void setFCount(int fCount) { this.fCount = fCount; }
    
    public int getTCount() { return tCount; }
    public void setTCount(int tCount) { this.tCount = tCount; }
    
    public String getSdate() { return sdate; }
    public void setSdate(String sdate) { this.sdate = sdate; }
    
    public String getEdate() { return edate; }
    public void setEdate(String edate) { this.edate = edate; }
    
    // SMS 관련 getter/setter
    public Integer getSmsTotal() { return smsTotal; }
    public void setSmsTotal(Integer smsTotal) { this.smsTotal = smsTotal; }
    
    public Integer getSmsSuccess() { return smsSuccess; }
    public void setSmsSuccess(Integer smsSuccess) { this.smsSuccess = smsSuccess; }
    
    public Integer getSmsFail() { return smsFail; }
    public void setSmsFail(Integer smsFail) { this.smsFail = smsFail; }
    
    public Integer getSmsSent() { return smsSent; }
    public void setSmsSent(Integer smsSent) { this.smsSent = smsSent; }
    
    // LMS 관련 getter/setter
    public Integer getLmsTotal() { return lmsTotal; }
    public void setLmsTotal(Integer lmsTotal) { this.lmsTotal = lmsTotal; }
    
    public Integer getLmsSuccess() { return lmsSuccess; }
    public void setLmsSuccess(Integer lmsSuccess) { this.lmsSuccess = lmsSuccess; }
    
    public Integer getLmsFail() { return lmsFail; }
    public void setLmsFail(Integer lmsFail) { this.lmsFail = lmsFail; }
    
    public Integer getLmsSent() { return lmsSent; }
    public void setLmsSent(Integer lmsSent) { this.lmsSent = lmsSent; }
    
    // MMS 관련 getter/setter
    public Integer getMmsTotal() { return mmsTotal; }
    public void setMmsTotal(Integer mmsTotal) { this.mmsTotal = mmsTotal; }
    
    public Integer getMmsSuccess() { return mmsSuccess; }
    public void setMmsSuccess(Integer mmsSuccess) { this.mmsSuccess = mmsSuccess; }
    
    public Integer getMmsFail() { return mmsFail; }
    public void setMmsFail(Integer mmsFail) { this.mmsFail = mmsFail; }
    
    public Integer getMmsSent() { return mmsSent; }
    public void setMmsSent(Integer mmsSent) { this.mmsSent = mmsSent; }
    
    // RCS-Template 관련 getter/setter
    public Integer getRcsTemplateTotal() { return rcsTemplateTotal; }
    public void setRcsTemplateTotal(Integer rcsTemplateTotal) { this.rcsTemplateTotal = rcsTemplateTotal; }
    
    public Integer getRcsTemplateSuccess() { return rcsTemplateSuccess; }
    public void setRcsTemplateSuccess(Integer rcsTemplateSuccess) { this.rcsTemplateSuccess = rcsTemplateSuccess; }
    
    public Integer getRcsTemplateFail() { return rcsTemplateFail; }
    public void setRcsTemplateFail(Integer rcsTemplateFail) { this.rcsTemplateFail = rcsTemplateFail; }
    
    public Integer getRcsTemplateSent() { return rcsTemplateSent; }
    public void setRcsTemplateSent(Integer rcsTemplateSent) { this.rcsTemplateSent = rcsTemplateSent; }
    
    // RCS-LMS-Template 관련 getter/setter
    public Integer getRcsLmsTemplateTotal() { return rcsLmsTemplateTotal; }
    public void setRcsLmsTemplateTotal(Integer rcsLmsTemplateTotal) { this.rcsLmsTemplateTotal = rcsLmsTemplateTotal; }
    
    public Integer getRcsLmsTemplateSuccess() { return rcsLmsTemplateSuccess; }
    public void setRcsLmsTemplateSuccess(Integer rcsLmsTemplateSuccess) { this.rcsLmsTemplateSuccess = rcsLmsTemplateSuccess; }
    
    public Integer getRcsLmsTemplateFail() { return rcsLmsTemplateFail; }
    public void setRcsLmsTemplateFail(Integer rcsLmsTemplateFail) { this.rcsLmsTemplateFail = rcsLmsTemplateFail; }
    
    public Integer getRcsLmsTemplateSent() { return rcsLmsTemplateSent; }
    public void setRcsLmsTemplateSent(Integer rcsLmsTemplateSent) { this.rcsLmsTemplateSent = rcsLmsTemplateSent; }
    
    // 알림톡 관련 getter/setter
    public Integer getAlimtalkTotal() { return alimtalkTotal; }
    public void setAlimtalkTotal(Integer alimtalkTotal) { this.alimtalkTotal = alimtalkTotal; }
    
    public Integer getAlimtalkSuccess() { return alimtalkSuccess; }
    public void setAlimtalkSuccess(Integer alimtalkSuccess) { this.alimtalkSuccess = alimtalkSuccess; }
    
    public Integer getAlimtalkFail() { return alimtalkFail; }
    public void setAlimtalkFail(Integer alimtalkFail) { this.alimtalkFail = alimtalkFail; }
    
    public Integer getAlimtalkSent() { return alimtalkSent; }
    public void setAlimtalkSent(Integer alimtalkSent) { this.alimtalkSent = alimtalkSent; }
    
    // 네이버톡 관련 getter/setter
    public Integer getNavertalkTotal() { return navertalkTotal; }
    public void setNavertalkTotal(Integer navertalkTotal) { this.navertalkTotal = navertalkTotal; }
    
    public Integer getNavertalkSuccess() { return navertalkSuccess; }
    public void setNavertalkSuccess(Integer navertalkSuccess) { this.navertalkSuccess = navertalkSuccess; }
    
    public Integer getNavertalkFail() { return navertalkFail; }
    public void setNavertalkFail(Integer navertalkFail) { this.navertalkFail = navertalkFail; }
    
    public Integer getNavertalkSent() { return navertalkSent; }
    public void setNavertalkSent(Integer navertalkSent) { this.navertalkSent = navertalkSent; }
}