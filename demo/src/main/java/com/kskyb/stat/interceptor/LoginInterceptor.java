package com.kskyb.stat.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 로그인 인증 인터셉터
 * 보호된 페이지 접근 시 로그인 여부를 확인
 */
public class LoginInterceptor implements HandlerInterceptor {

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        
        String requestURI = request.getRequestURI();
        
        // 로그인 관련 페이지는 인터셉터 제외
        if (requestURI.contains("/login") || 
            requestURI.contains("/auth/") ||
            requestURI.contains("/css/") || 
            requestURI.contains("/js/") || 
            requestURI.contains("/images/") ||
            requestURI.contains("/static/")) {
            return true;
        }
        
        HttpSession session = request.getSession();
        String userId = (String) session.getAttribute("userId");
        
        if (userId == null) {
            // 원래 요청한 URL을 세션에 저장 (로그인 후 리다이렉트용)
            String targetUrl = requestURI;
            if (request.getQueryString() != null) {
                targetUrl += "?" + request.getQueryString();
            }
            session.setAttribute("targetUrl", targetUrl);
            
            // AJAX 요청인 경우
            if ("XMLHttpRequest".equals(request.getHeader("X-Requested-With"))) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("{\"error\":\"로그인이 필요합니다.\"}");
                return false;
            }
            
            // 일반 페이지 요청인 경우 로그인 페이지로 리다이렉트
            response.sendRedirect(request.getContextPath() + "/login");
            return false;
        }
        
        // 세션 타임아웃 체크 (30분)
        Long loginTime = (Long) session.getAttribute("loginTime");
        if (loginTime != null) {
            long currentTime = System.currentTimeMillis();
            long sessionTimeout = 30 * 60 * 1000; // 30분
            
            if (currentTime - loginTime > sessionTimeout) {
                session.invalidate();
                response.sendRedirect(request.getContextPath() + "/login?timeout=true");
                return false;
            }
        }
        
        return true;
    }
}