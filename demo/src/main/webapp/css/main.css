/* KSKYB 메시지 통계 시스템 - 통합 CSS */

:root {
    --primary-color: #2563eb;
    --sidebar-width: 260px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background-color: #f8f9fa !important;
    margin: 0;
    padding: 0;
    color: #212529 !important;
    font-size: 1rem !important;
}

/* 사이드바 */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, #1e3a8a 0%, #2563eb 100%);
    z-index: 1000;
    overflow-y: auto;
}

.sidebar-brand {
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.sidebar-brand h3 {
    color: white;
    margin: 0;
    font-weight: bold;
}

/* 네비게이션 섹션 스타일 개선 */
.nav-section {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.nav-link {
    color: rgba(255,255,255,0.8) !important;
    padding: 0.75rem 1.5rem;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.1);
    border-left-color: rgba(255,255,255,0.5);
    text-decoration: none;
}

.nav-link.active {
    color: white !important;
    background: rgba(255,255,255,0.15);
    border-left-color: white;
    font-weight: 600;
}

/* 서브메뉴 스타일 */
.nav-submenu {
    background: rgba(255,255,255,0.05) !important;
}

.nav-submenu .nav-link {
    padding-left: 3rem;
    font-size: 0.9rem;
    border-left-width: 2px;
}

.nav-submenu .nav-link:hover {
    background: rgba(255,255,255,0.1) !important;
    padding-left: 3.2rem;
}

.nav-submenu .nav-link.active {
    background: rgba(255,255,255,0.15) !important;
    border-left-color: #fbbf24;
    font-weight: 600;
}

/* 통계 메뉴 아이콘 회전 효과 */
.nav-link[data-bs-toggle="collapse"] .fa-chevron-down {
    transition: transform 0.3s ease;
}

.nav-link[data-bs-toggle="collapse"][aria-expanded="true"] .fa-chevron-down {
    transform: rotate(180deg);
}

/* 로그아웃 버튼 */
.logout-section {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
}

.logout-btn {
    width: 100%;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 1);
    color: white;
    transform: translateY(-2px);
}

/* 메인 콘텐츠 */
.main-content {
    margin-left: var(--sidebar-width);
    padding: 2rem;
    min-height: 100vh;
}

/* 페이지 헤더 */
.page-header {
    background: #fff !important;
    padding: 1.5rem 0 !important;
    border-bottom: 1px solid #dee2e6 !important;
    margin-bottom: 2rem !important;
}

.sub-nav-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.nav-depth {
    color: #6c757d;
    font-size: 0.875rem;
}

/* 검색 폼 스타일 */
.search-wrapper {
    background: #fff !important;
    border-radius: 10px !important;
    padding: 1.5rem !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    margin-bottom: 1.5rem !important;
}

.search-wrapper .form-inline .form-group {
    margin-right: 1rem;
    margin-bottom: 0.5rem;
}

.search-wrapper .form-inline fieldset {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

/* 카드 스타일 */
.card {
    border: none !important;
    border-radius: 10px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    margin-bottom: 1.5rem !important;
    background: #fff !important;
}

.card-header {
    background: var(--primary-color) !important;
    color: white !important;
    border-radius: 10px 10px 0 0 !important;
    padding: 1rem 1.5rem !important;
    border-bottom: none !important;
}

.card-header h5 {
    color: white !important;
    margin: 0 !important;
    font-weight: 600 !important;
}

.card-body {
    padding: 1.5rem !important;
}

/* 콘텐츠 래퍼 */
.content-wrapper {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.panel {
    border: none;
}

.panel-default {
    border-color: transparent;
}

/* 테이블 스타일 강화 */
.table {
    margin-bottom: 0;
    table-layout: fixed !important; /* 테이블 레이아웃 고정 */
    font-size: 0.9rem !important;
    color: #212529 !important;
}

.table thead th {
    background: #343a40 !important;
    color: white !important;
    border: none !important;
    text-align: center !important;
    vertical-align: middle !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important; /* 폰트 크기 조정 */
    padding: 1rem 0.5rem !important;
}

.table tbody td {
    text-align: center !important;
    vertical-align: middle !important;
    border-color: #dee2e6 !important;
    padding: 0.75rem 0.5rem !important;
    font-size: 0.875rem !important;
    color: #212529 !important;
}

/* 통계 화면 테이블 컬럼 너비 (18개 컬럼) - 수정된 너비 */
.table th:nth-child(1), .table td:nth-child(1) { width: 5% !important; } /* 업체명 */
.table th:nth-child(2), .table td:nth-child(2) { width: 4% !important; } /* 파트너ID */
.table th:nth-child(3), .table td:nth-child(3) { width: 5% !important; } /* 날짜 */

/* SMS, LMS, MMS, 알림톡, 네이버톡 각각 3개 컬럼씩 동일한 너비 */
.table th:nth-child(4), .table td:nth-child(4),   /* SMS 발송 */
.table th:nth-child(5), .table td:nth-child(5),   /* SMS 성공 */
.table th:nth-child(6), .table td:nth-child(6),   /* SMS 실패 */
.table th:nth-child(7), .table td:nth-child(7),   /* LMS 발송 */
.table th:nth-child(8), .table td:nth-child(8),   /* LMS 성공 */
.table th:nth-child(9), .table td:nth-child(9),   /* LMS 실패 */
.table th:nth-child(10), .table td:nth-child(10), /* MMS 발송 */
.table th:nth-child(11), .table td:nth-child(11), /* MMS 성공 */
.table th:nth-child(12), .table td:nth-child(12), /* MMS 실패 */
.table th:nth-child(13), .table td:nth-child(13), /* 알림톡 발송 */
.table th:nth-child(14), .table td:nth-child(14), /* 알림톡 성공 */
.table th:nth-child(15), .table td:nth-child(15), /* 알림톡 실패 */
.table th:nth-child(16), .table td:nth-child(16), /* 네이버톡 발송 */
.table th:nth-child(17), .table td:nth-child(17), /* 네이버톡 성공 */
.table th:nth-child(18), .table td:nth-child(18)  /* 네이버톡 실패 */
{ 
    width: 5.7% !important; /* 모든 발송/성공/실패 컬럼 동일한 너비 (늘림) */
    min-width: 70px !important; /* 최소 너비 보장 */
    font-size: 0.875rem !important;
    color: #212529 !important;
}

/* 업체 관리 화면용 테이블 (5개 컬럼) - 필요시 별도 클래스로 사용 */
.company-management-table th:nth-child(1), 
.company-management-table td:nth-child(1) { width: 25%; } /* 업체명 */
.company-management-table th:nth-child(2), 
.company-management-table td:nth-child(2) { width: 20%; } /* 파트너ID */
.company-management-table th:nth-child(3), 
.company-management-table td:nth-child(3) { width: 20%; } /* 시작일 */
.company-management-table td:nth-child(4) { width: 20%; } /* 종료일 */
.company-management-table th:nth-child(5), 
.company-management-table td:nth-child(5) { width: 15%; } /* 작업 */

/* 기존 company-table 클래스는 호환성을 위해 유지 */
.company-table th:nth-child(1), .company-table td:nth-child(1) { width: 20%; } /* 업체명 */
.company-table th:nth-child(2), .company-table td:nth-child(2) { width: 15%; } /* 파트너ID */
.company-table th:nth-child(3), .company-table td:nth-child(3) { width: 25%; } /* 시작일 */
.company-table th:nth-child(4), .company-table td:nth-child(4) { width: 25%; } /* 종료일 */
.company-table th:nth-child(5), .company-table td:nth-child(5) { width: 15%; } /* 작업 */

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

.table-responsive {
    border-radius: 10px !important;
    border: none !important;
}

/* 테이블 테두리 스타일 (HTML에서 사용된 border-start, border-end 클래스) */
.border-start {
    border-left: 1px solid #dee2e6 !important;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

/* 성공/실패 텍스트 색상 */
.text-success {
    color: #198754 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* 합계 행 스타일 */
.table-warning {
    background-color: #fff3cd !important;
}

.fw-bold {
    font-weight: bold !important;
}

/* 버튼 스타일 강화 */
.btn {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.btn-primary {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

.btn-primary:hover {
    background: #1d4ed8 !important;
    border-color: #1d4ed8 !important;
    color: white !important;
    transform: translateY(-1px);
}

.btn-success {
    background: #198754 !important;
    border-color: #198754 !important;
    color: white !important;
}

.btn-success:hover {
    background: #157347 !important;
    border-color: #146c43 !important;
    color: white !important;
}

.btn-default {
    background: #fff;
    border-color: #dee2e6;
    color: #495057;
    transition: all 0.3s ease;
}

.btn-default:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

.btn-search {
    background: #28a745;
    border-color: #28a745;
    color: white;
    min-width: 120px;
}

.btn-search:hover {
    background: #218838;
    border-color: #1e7e34;
    color: white;
}

.btn-reset {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
    min-width: 120px;
}

.btn-reset:hover {
    background: #5a6268;
    border-color: #545b62;
    color: white;
}

.btn-info {
    background: #17a2b8;
    border-color: #17a2b8;
}

.btn-info:hover {
    background: #138496;
    border-color: #117a8b;
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
}

/* 폼 컨트롤 스타일 강화 */
.form-control {
    border-radius: 8px !important;
    border: 1px solid #dee2e6 !important;
    transition: all 0.3s ease !important;
    font-size: 0.875rem !important;
    color: #495057 !important;
    background-color: #fff !important;
}

.form-control:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25) !important;
    color: #495057 !important;
}

.form-select {
    border-radius: 8px !important;
    border: 1px solid #dee2e6 !important;
    font-size: 0.875rem !important;
    color: #495057 !important;
    background-color: #fff !important;
}

.form-label {
    font-weight: 500 !important;
    color: #495057 !important;
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
}

/* 텍스트 색상 강화 */
h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
}

.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: #198754 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* 배지 스타일 */
.badge {
    font-size: 0.75rem !important;
    font-weight: 500 !important;
}

.bg-info {
    background-color: #0dcaf0 !important;
    color: #000 !important;
}

/* 현재 시간 표시 스타일 */
#currentTime {
    font-size: 0.875rem !important;
    color: #6c757d !important;
}

/* 폼 체크 스타일 */
.form-check-label {
    font-size: 0.875rem !important;
    color: #495057 !important;
    cursor: pointer !important;
}

.form-check-input {
    cursor: pointer !important;
}

/* Bootstrap Datepicker 스타일 강화 */
.datepicker {
    z-index: 2000 !important;
    font-size: 0.875rem !important;
}

.datepicker table {
    font-size: 0.875rem !important;
}

.datepicker td, .datepicker th {
    text-align: center !important;
    width: 30px !important;
    height: 30px !important;
    border-radius: 4px !important;
}

.datepicker .active {
    background-color: var(--primary-color) !important;
    color: white !important;
}

.datepicker .active:hover {
    background-color: #1d4ed8 !important;
}

.datepicker .today {
    background-color: #f0f9ff !important;
    color: var(--primary-color) !important;
}

.datepicker .today:hover {
    background-color: #dbeafe !important;
}

/* 날짜 입력 필드 커서 */
.form-control[readonly] {
    cursor: pointer !important;
    background-color: #fff !important;
}

/* 반응형 강화 */
@media (max-width: 768px) {
    .sidebar {
        margin-left: calc(-1 * var(--sidebar-width));
        transition: margin-left 0.3s ease;
    }
    
    .sidebar.show {
        margin-left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .search-wrapper {
        padding: 1rem;
    }
    
    .search-wrapper .form-inline fieldset {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-wrapper .form-inline .form-group {
        margin-right: 0;
        margin-bottom: 1rem;
        width: 100%;
    }
    
    .pull-right {
        float: none;
        margin-top: 1rem;
    }

    .company-table th,
    .company-table td {
        min-width: 80px;
        font-size: 0.8rem;
        padding: 0.5rem 0.25rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-xs {
        padding: 0.125rem 0.25rem;
        font-size: 0.6875rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 1rem 0;
    }
    
    .sub-nav-title {
        font-size: 1.25rem;
    }
    
    .company-table th,
    .company-table td {
        font-size: 0.75rem;
        padding: 0.375rem 0.125rem;
    }
}