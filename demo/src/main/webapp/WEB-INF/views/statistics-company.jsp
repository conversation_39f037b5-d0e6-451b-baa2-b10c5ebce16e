<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="f" uri="http://www.springframework.org/tags/form" %>
<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KSKYB - 업체별 통계 조회</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap Datepicker CSS - 로컬 파일 사용 -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/bootstrap-datepicker.min.css">
    <!-- 커스텀 CSS (Bootstrap 다음에 로드하여 우선순위 확보) -->
    <link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/css/main.css">
</head>
<body>
    <!-- 사이드바 -->
    <div class="sidebar" id="sidebar">
        <!-- 브랜드 -->
        <div class="sidebar-brand">
            <h3><i class="fas fa-chart-bar me-2"></i>KSKYB</h3>
        </div>

        <!-- 네비게이션 -->
        <nav class="nav flex-column">
            <!-- 통계 메뉴 -->
            <div class="nav-section">
                <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#statisticsMenu">
                    <i class="fas fa-chart-line me-2"></i>통계
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse show nav-submenu" id="statisticsMenu">
                    <a class="nav-link active" href="${pageContext.request.contextPath}/stats/company">
                        <i class="fas fa-building me-2"></i>업체별 조회1
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-chart-bar me-2"></i>업체별 조회2
                    </a>
                    <a class="nav-link" href="#">
                        <i class="fas fa-network-wired me-2"></i>전송라인별 조회
                    </a>
                </div>
            </div>

            <!-- 정산 메뉴 -->
            <div class="nav-section">
                <a class="nav-link" href="#">
                    <i class="fas fa-calculator me-2"></i>정산
                </a>
            </div>

            <!-- 고객사관리 메뉴 -->
            <div class="nav-section">
                <a class="nav-link" href="#">
                    <i class="fas fa-users me-2"></i>고객사관리
                </a>
            </div>
        </nav>

        <!-- 로그아웃 섹션 -->
        <div class="logout-section">
            <button class="btn logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt me-2"></i>로그아웃
            </button>
        </div>
    </div>

    <!-- 메인 콘텐츠 -->
    <div class="main-content">
        <!-- 모바일 메뉴 버튼 -->
        <button class="btn btn-primary d-md-none mb-3" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
            <i class="fas fa-bars"></i> 메뉴
        </button>

        <!-- 페이지 헤더 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-building text-primary me-2"></i>업체별 통계 조회</h2>
                <p class="text-muted">업체별 메시지 발송 통계를 조회합니다</p>
            </div>
            <div class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                <span id="currentTime"></span>
            </div>
        </div>

        <!-- 검색 영역 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>검색 조건</h5>
            </div>
            <div class="card-body">
                <f:form modelAttribute="searchRequest" method="post" role="form" action="${pageContext.request.contextPath}/stats/company/search">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-2">
                            <label class="form-label">시작일</label>
                            <f:input type="text" id="sdate" path="sDate" class="form-control" placeholder="시작일" autocomplete="false" readonly="true"/>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">종료일</label>
                            <f:input type="text" id="edate" path="eDate" class="form-control" placeholder="종료일" autocomplete="false" readonly="true"/>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">업체명</label>
                            <f:select id="cName" path="cName" class="form-select">
                                <f:option value="">전체</f:option>
                                <c:forEach var="company" items="${companyList}">
                                    <f:option value="${company.cName}">${company.cName}</f:option>
                                </c:forEach>
                            </f:select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">조회 단위</label>
                            <div class="mt-2">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="periodType" value="daily" checked>
                                    <label class="form-check-label">일별</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="periodType" value="monthly">
                                    <label class="form-check-label">월별</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>조회
                                </button>
                            </div>
                        </div>
                    </div>
                </f:form>
            </div>
        </div>

        <!-- 결과 테이블 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>조회 결과</h5>
                <div>
                    <span class="badge bg-info me-2">총 <span id="totalCount">${fn:length(statisticsDataList)}</span>건</span>
                    <button class="btn btn-success btn-sm" onclick="downloadExcel()">
                        <i class="fas fa-file-excel me-1"></i>엑셀 다운로드
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th rowspan="2" class="border-end">업체명</th>
                                <th rowspan="2" class="border-end">파트너ID</th>
                                <th rowspan="2" class="border-end">날짜</th>
                                <th colspan="3" class="border-end">SMS</th>
                                <th colspan="3" class="border-end">LMS</th>
                                <th colspan="3" class="border-end">MMS</th>
                                <th colspan="3" class="border-end">RCS-LMS-Template</th>
                                <th colspan="3" class="border-end">RCS-Template</th>
                                <th colspan="3" class="border-end">알림톡</th>
                                <th colspan="3" class="border-end">네이버톡</th>
                            </tr>
                            <tr>
                                <th class="border-start">발송</th><th>성공</th><th class="border-end">실패</th>
                                <th class="border-start">발송</th><th>성공</th><th class="border-end">실패</th>
                                <th class="border-start">발송</th><th>성공</th><th class="border-end">실패</th>
                                <th class="border-start">발송</th><th>성공</th><th class="border-end">실패</th>
                                <th class="border-start">발송</th><th>성공</th><th class="border-end">실패</th>
                                <th class="border-start">발송</th><th>성공</th><th class="border-end">실패</th>
                                <th class="border-start">발송</th><th>성공</th><th>실패</th>
                            </tr>
                        </thead>
                        <tbody id="resultTable">
                            <c:if test="${statisticsDataList != null and fn:length(statisticsDataList) > 0}">
                                <c:forEach items="${statisticsDataList}" var="stat" varStatus="status">
                                <tr>
                                    <td class="border-end">${stat.testCName}</td>  <!-- 테스트용 메서드 사용 -->
                                    <td class="border-end">${stat.ptnId}</td>
                                    <td class="border-end">${stat.testCDate}</td>  <!-- cDate 대신 testCDate 사용 -->
                                    <!-- SMS -->
                                    <td class="border-start"><fmt:formatNumber value="${stat.smsTotal}" pattern="#,###" /></td>
                                    <td class="text-success"><fmt:formatNumber value="${stat.smsSuccess}" pattern="#,###" /></td>
                                    <td class="text-danger border-end"><fmt:formatNumber value="${stat.smsFail}" pattern="#,###" /></td>
                                    <!-- LMS -->
                                    <td class="border-start"><fmt:formatNumber value="${stat.lmsTotal}" pattern="#,###" /></td>
                                    <td class="text-success"><fmt:formatNumber value="${stat.lmsSuccess}" pattern="#,###" /></td>
                                    <td class="text-danger border-end"><fmt:formatNumber value="${stat.lmsFail}" pattern="#,###" /></td>
                                    <!-- MMS -->
                                    <td class="border-start"><fmt:formatNumber value="${stat.mmsTotal}" pattern="#,###" /></td>
                                    <td class="text-success"><fmt:formatNumber value="${stat.mmsSuccess}" pattern="#,###" /></td>
                                    <td class="text-danger border-end"><fmt:formatNumber value="${stat.mmsFail}" pattern="#,###" /></td>
                                    <!-- RCS-LMS-Template -->
                                    <td class="border-start"><fmt:formatNumber value="${stat.rcsLmsTemplateTotal}" pattern="#,###" /></td>
                                    <td class="text-success"><fmt:formatNumber value="${stat.rcsLmsTemplateSuccess}" pattern="#,###" /></td>
                                    <td class="text-danger border-end"><fmt:formatNumber value="${stat.rcsLmsTemplateFail}" pattern="#,###" /></td>
                                    <!-- RCS-Template -->
                                    <td class="border-start"><fmt:formatNumber value="${stat.rcsTemplateTotal}" pattern="#,###" /></td>
                                    <td class="text-success"><fmt:formatNumber value="${stat.rcsTemplateSuccess}" pattern="#,###" /></td>
                                    <td class="text-danger border-end"><fmt:formatNumber value="${stat.rcsTemplateFail}" pattern="#,###" /></td>
                                    <!-- 알림톡 -->
                                    <td class="border-start"><fmt:formatNumber value="${stat.alimtalkTotal}" pattern="#,###" /></td>
                                    <td class="text-success"><fmt:formatNumber value="${stat.alimtalkSuccess}" pattern="#,###" /></td>
                                    <td class="text-danger border-end"><fmt:formatNumber value="${stat.alimtalkFail}" pattern="#,###" /></td>
                                    <!-- 네이버톡 -->
                                    <td class="border-start"><fmt:formatNumber value="${stat.navertalkTotal}" pattern="#,###" /></td>
                                    <td class="text-success"><fmt:formatNumber value="${stat.navertalkSuccess}" pattern="#,###" /></td>
                                    <td class="text-danger"><fmt:formatNumber value="${stat.navertalkFail}" pattern="#,###" /></td>
                                </tr>
                                </c:forEach>
                            </c:if>
                            <c:if test="${statisticsDataList == null or fn:length(statisticsDataList) == 0}">
                                <tr>
                                    <td colspan="21" class="text-center py-4 text-muted">
                                        <c:choose>
                                            <c:when test="${param.searched eq 'true' or not empty param.cName or not empty param.sDate or not empty param.eDate}">
                                                <i class="fas fa-search text-muted" style="font-size: 2rem;"></i>
                                                <p class="text-muted mt-2 mb-0">검색 조건에 해당하는 데이터가 없습니다.</p>
                                            </c:when>
                                            <c:otherwise>
                                                <i class="fas fa-info-circle me-2"></i>검색 조건을 입력하고 조회 버튼을 클릭해주세요.
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </c:if>
                        </tbody>
                        <c:if test="${statisticsDataList != null and fn:length(statisticsDataList) > 0}">
                        <tfoot id="resultTableFooter" class="table-warning fw-bold">
                            <tr>
                                <td colspan="3" class="text-center border-end">합계</td>
                                <!-- SMS 합계 -->
                                <td class="text-center border-start">
                                    <fmt:formatNumber value="${totalStats.smsTotal}" pattern="#,###" />
                                </td>
                                <td class="text-center text-success">
                                    <fmt:formatNumber value="${totalStats.smsSuccess}" pattern="#,###" />
                                </td>
                                <td class="text-center text-danger border-end">
                                    <fmt:formatNumber value="${totalStats.smsFail}" pattern="#,###" />
                                </td>
                                <!-- LMS 합계 -->
                                <td class="text-center border-start">
                                    <fmt:formatNumber value="${totalStats.lmsTotal}" pattern="#,###" />
                                </td>
                                <td class="text-center text-success">
                                    <fmt:formatNumber value="${totalStats.lmsSuccess}" pattern="#,###" />
                                </td>
                                <td class="text-center text-danger border-end">
                                    <fmt:formatNumber value="${totalStats.lmsFail}" pattern="#,###" />
                                </td>
                                <!-- MMS 합계 -->
                                <td class="text-center border-start">
                                    <fmt:formatNumber value="${totalStats.mmsTotal}" pattern="#,###" />
                                </td>
                                <td class="text-center text-success">
                                    <fmt:formatNumber value="${totalStats.mmsSuccess}" pattern="#,###" />
                                </td>
                                <td class="text-center text-danger border-end">
                                    <fmt:formatNumber value="${totalStats.mmsFail}" pattern="#,###" />
                                </td>
                                <!-- RCS-LMS-Template 합계 -->
                                <td class="text-center border-start">
                                    <fmt:formatNumber value="${totalStats.rcsLmsTemplateTotal}" pattern="#,###" />
                                </td>
                                <td class="text-center text-success">
                                    <fmt:formatNumber value="${totalStats.rcsLmsTemplateSuccess}" pattern="#,###" />
                                </td>
                                <td class="text-center text-danger border-end">
                                    <fmt:formatNumber value="${totalStats.rcsLmsTemplateFail}" pattern="#,###" />
                                </td>
                                <!-- RCS-Template 합계 -->
                                <td class="text-center border-start">
                                    <fmt:formatNumber value="${totalStats.rcsTemplateTotal}" pattern="#,###" />
                                </td>
                                <td class="text-center text-success">
                                    <fmt:formatNumber value="${totalStats.rcsTemplateSuccess}" pattern="#,###" />
                                </td>
                                <td class="text-center text-danger border-end">
                                    <fmt:formatNumber value="${totalStats.rcsTemplateFail}" pattern="#,###" />
                                </td>
                                <!-- 알림톡 합계 -->
                                <td class="text-center border-start">
                                    <fmt:formatNumber value="${totalStats.alimtalkTotal}" pattern="#,###" />
                                </td>
                                <td class="text-center text-success">
                                    <fmt:formatNumber value="${totalStats.alimtalkSuccess}" pattern="#,###" />
                                </td>
                                <td class="text-center text-danger border-end">
                                    <fmt:formatNumber value="${totalStats.alimtalkFail}" pattern="#,###" />
                                </td>
                                <!-- 네이버톡 합계 -->
                                <td class="text-center border-start">
                                    <fmt:formatNumber value="${totalStats.navertalkTotal}" pattern="#,###" />
                                </td>
                                <td class="text-center text-success">
                                    <fmt:formatNumber value="${totalStats.navertalkSuccess}" pattern="#,###" />
                                </td>
                                <td class="text-center text-danger">
                                    <fmt:formatNumber value="${totalStats.navertalkFail}" pattern="#,###" />
                                </td>
                            </tr>
                        </tfoot>
                        </c:if>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Datepicker JS - 로컬 파일 사용 -->
    <script src="${pageContext.request.contextPath}/js/bootstrap-datepicker.min.js"></script>
    <script src="${pageContext.request.contextPath}/js/bootstrap-datepicker.ko.min.js"></script>
    
    <script>
        // 현재 시간 표시
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ko-KR');
            document.getElementById('currentTime').textContent = timeString;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 로그아웃 함수
        function logout() {
            if (confirm('로그아웃 하시겠습니까?')) {
                window.location.href = '${pageContext.request.contextPath}/logout';
            }
        }

        // 엑셀 다운로드 함수
        function downloadExcel() {
            // 조회된 데이터가 있는지 확인
            var tableBody = document.getElementById('resultTable');
            var rows = tableBody.getElementsByTagName('tr');
            
            if (rows.length === 0 || (rows.length === 1 && rows[0].cells.length === 1)) {
                alert('다운로드할 데이터가 없습니다. 먼저 조회를 수행해주세요.');
                return;
            }
            
            // 현재 폼의 값들을 가져와서 엑셀 다운로드 요청
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '${pageContext.request.contextPath}/stats/company/excel';
            
            // 검색 조건들을 hidden input으로 추가
            var sDate = document.getElementById('sdate').value;
            var eDate = document.getElementById('edate').value;
            var cName = document.getElementById('cName').value;
            
            if (sDate) {
                var sdateInput = document.createElement('input');
                sdateInput.type = 'hidden';
                sdateInput.name = 'sDate';
                sdateInput.value = sDate;
                form.appendChild(sdateInput);
            }
            
            if (eDate) {
                var edateInput = document.createElement('input');
                edateInput.type = 'hidden';
                edateInput.name = 'eDate';
                edateInput.value = eDate;
                form.appendChild(edateInput);
            }
            
            if (cName) {
                var cnameInput = document.createElement('input');
                cnameInput.type = 'hidden';
                cnameInput.name = 'cName';
                cnameInput.value = cName;
                form.appendChild(cnameInput);
            }
            
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        // jQuery 문서 준비 완료 후 실행
        $(document).ready(function() {
            // datepicker 플러그인이 로드되었는지 확인
            if (typeof $.fn.datepicker === 'undefined') {
                console.error('Bootstrap Datepicker plugin is not loaded');
                return;
            }

            // 날짜 선택기 초기화
            $('#sdate, #edate').datepicker({
                format: 'yyyy-mm-dd',
                language: 'ko',
                autoclose: true,
                todayHighlight: true,
                orientation: 'bottom auto',
                container: 'body',
                zIndexOffset: 1050,
                weekStart: 0
            }).on('show', function(e) {
                // datepicker가 표시될 때 z-index 조정
                $('.datepicker').css('z-index', 9999);
            });

            // 기본값 설정 (오늘 날짜)
            var today = new Date();
            var todayStr = today.getFullYear() + '-' + 
                          String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                          String(today.getDate()).padStart(2, '0');
            
            // 시작일과 종료일이 비어있으면 오늘 날짜로 설정
            if (!$('#sdate').val()) {
                $('#sdate').val(todayStr);
            }
            if (!$('#edate').val()) {
                $('#edate').val(todayStr);
            }

            // 시작일 변경 시 종료일 최소값 설정
            $('#sdate').on('changeDate', function(e) {
                var startDate = e.date;
                $('#edate').datepicker('setStartDate', startDate);
                
                // 종료일이 시작일보다 이전이면 시작일과 같게 설정
                var endDateValue = $('#edate').datepicker('getDate');
                if (endDateValue && endDateValue < startDate) {
                    $('#edate').datepicker('setDate', startDate);
                }
            });

            // 종료일 변경 시 시작일 최대값 설정
            $('#edate').on('changeDate', function(e) {
                var endDate = e.date;
                $('#sdate').datepicker('setEndDate', endDate);
                
                // 시작일이 종료일보다 이후면 종료일과 같게 설정
                var startDateValue = $('#sdate').datepicker('getDate');
                if (startDateValue && startDateValue > endDate) {
                    $('#sdate').datepicker('setDate', endDate);
                }
            });

            // 폼 제출 시 유효성 검사
            $('form').on('submit', function(e) {
                var sDate = $('#sdate').val();
                var eDate = $('#edate').val();
                
                if (!sDate || !eDate) {
                    e.preventDefault();
                    alert('시작일과 종료일을 모두 선택해주세요.');
                    return false;
                }
                
                // 날짜 형식 검증
                var dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (!dateRegex.test(sDate) || !dateRegex.test(eDate)) {
                    e.preventDefault();
                    alert('날짜 형식이 올바르지 않습니다. (YYYY-MM-DD)');
                    return false;
                }
                
                // 시작일이 종료일보다 이후인지 검사
                if (new Date(sDate) > new Date(eDate)) {
                    e.preventDefault();
                    alert('시작일은 종료일보다 이전이어야 합니다.');
                    return false;
                }
                
                return true;
            });

            // 모바일 환경에서 사이드바 토글
            $('.btn[data-bs-toggle="collapse"]').on('click', function() {
                $('#sidebar').toggleClass('show');
            });
        });
    </script>
</body>
</html>