<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KSKYB - 메시지 통계 시스템 로그인</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* 파란색 그라데이션 배경 */
        .bg-gradient-primary {
            background: linear-gradient(180deg, #4e73df 10%, #224abe 100%) !important;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        /* 로그인 카드 스타일 */
        .card {
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            border: none;
        }
        
        /* 로그인창 크기 조정 - 넓게 */
        .col-xl-12 {
            max-width: 1200px;
        }
        
        /* 왼쪽 파란색 이미지 영역 */
        .bg-login-image {
            background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
            position: relative;
        }
        
        /* 브랜드 영역 중앙 정렬 */
        .login-brand-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            text-align: center;
            padding: 2rem;
        }
        
        /* 브랜드 텍스트 스타일 */
        .login-brand-area h2 {
            font-size: 2.5rem;
            font-weight: 900 !important;
            letter-spacing: 2px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
            color: white;
        }
        
        .login-brand-area p {
            font-size: 1.1rem;
            opacity: 0.9;
            color: rgba(255,255,255,0.8);
        }
        
        .login-brand-area i {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            color: white;
        }
        
        /* 컨테이너 전체 너비 */
        .container {
            width: 100%;
            max-width: none;
        }
        
        /* 로그인 폼 오른쪽 영역 */
        .col-lg-6 .p-5 {
            padding: 3rem !important;
        }
        
        /* 폼 스타일 개선 */
        .form-control {
            border-radius: 0.5rem;
            border: 2px solid #e3e6f0;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }
        
        .input-group-text {
            background-color: #f8f9fc;
            border: 2px solid #e3e6f0;
            border-right: none;
            color: #5a5c69;
        }
        
        /* 비밀번호 토글 버튼 스타일 - 왼쪽 input-group-text와 일치 */
        #togglePassword {
            background-color: #f8f9fc;
            border: 2px solid #e3e6f0;
            border-left: none;
            color: #5a5c69;
            border-radius: 0 0.375rem 0.375rem 0;
        }
        
        #togglePassword:hover {
            background-color: #e9ecef;
            color: #495057;
        }
        
        #togglePassword:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }
        
        /* 버튼 스타일 */
        .btn-primary {
            background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
        }
        
        .btn-success {
            background: linear-gradient(180deg, #1cc88a 10%, #13855c 100%);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
        }
        
        .btn-user {
            font-size: 0.8rem;
            border-radius: 10rem;
            padding: 0.75rem 1rem;
        }
        
        /* SMS 인증 영역 */
        #smsAuthSection {
            background-color: #f8f9fc;
            border: 2px solid #e3e6f0;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        /* 반응형 디자인 */
        @media (max-width: 992px) {
            .bg-login-image {
                display: none !important;
            }
            
            .col-lg-6 .p-5 {
                padding: 2rem !important;
            }
            
            .login-brand-area h2 {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 576px) {
            .col-lg-6 .p-5 {
                padding: 1.5rem !important;
            }
            
            .card {
                margin: 1rem !important;
            }
        }
    </style>
</head>
<body class="bg-gradient-primary">
    <div class="container">
        <!-- 로그인 폼 -->
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-9">
                <div class="card o-hidden border-0 shadow-lg my-5">
                    <div class="card-body p-0">
                        <div class="row">
                            <!-- 왼쪽 이미지 영역 -->
                            <div class="col-lg-6 d-none d-lg-block bg-login-image">
                                <div class="login-brand-area">
                                    <div class="text-center">
                                        <i class="fas fa-chart-bar fa-5x text-white mb-4"></i>
                                        <h2 class="text-white font-weight-bold"><strong>KSKYB STATISTICS</strong></h2>
                                        <p class="text-white-50">메시지 발송 통계 관리 플랫폼</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 오른쪽 로그인 폼 영역 -->
                            <div class="col-lg-6">
                                <div class="p-5">
                                    <div class="text-center">
                                        <h1 class="h4 text-gray-900 mb-4 font-weight-bold">로그인</h1>
                                    </div>
                                    
                                    <!-- 에러 메시지 표시 -->
                                    <c:if test="${not empty error}">
                                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            ${error}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    </c:if>
                                    
                                    <!-- 로그인 폼 -->
                                    <form class="user" id="loginForm" action="/demo/login" method="post">
                                        <!-- 아이디 입력 -->
                                        <div class="form-group mb-3">
                                            <label for="userId" class="form-label">아이디</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-user"></i>
                                                </span>
                                                <input type="text" 
                                                       class="form-control form-control-user" 
                                                       id="userId" 
                                                       name="userId"
                                                       placeholder="아이디를 입력하세요"
                                                       value="${param.userId}"
                                                       required>
                                            </div>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                        
                                        <!-- 비밀번호 입력 -->
                                        <div class="form-group mb-3">
                                            <label for="password" class="form-label">비밀번호</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-lock"></i>
                                                </span>
                                                <input type="password" 
                                                       class="form-control form-control-user" 
                                                       id="password" 
                                                       name="password"
                                                       placeholder="비밀번호를 입력하세요"
                                                       required>
                                                <button class="btn btn-outline-secondary" 
                                                        type="button" 
                                                        id="togglePassword">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                        
                                        <!-- SMS 인증 영역 (비밀번호 확인 후 활성화) -->
                                        <div class="form-group mb-3" id="smsAuthSection" style="display: none;">
                                            <label for="phoneNumber" class="form-label">
                                                등록된 휴대폰 번호로 인증번호가 발송되었습니다
                                            </label>
                                            <div class="alert alert-info small mb-2">
                                                <i class="fas fa-info-circle me-1"></i>
                                                보안을 위해 등록된 휴대폰 번호의 뒷 4자리만 표시됩니다: 010-****-<span id="phoneLastDigits">****</span>
                                            </div>
                                            
                                            <div class="input-group mb-2" id="verificationCodeSection">
                                                <span class="input-group-text">
                                                    <i class="fas fa-key"></i>
                                                </span>
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="verificationCode" 
                                                       name="verificationCode"
                                                       placeholder="인증번호 6자리를 입력하세요"
                                                       maxlength="6">
                                                <button type="button" 
                                                        class="btn btn-primary" 
                                                        id="verifyCodeBtn">
                                                    <i class="fas fa-check me-1"></i>
                                                    인증 확인
                                                </button>
                                            </div>
                                            
                                            <!-- 재발송 버튼 -->
                                            <div class="d-flex justify-content-between align-items-center">
                                                <button type="button" 
                                                        class="btn btn-outline-primary btn-sm" 
                                                        id="resendSmsBtn">
                                                    <i class="fas fa-redo me-1"></i>
                                                    인증번호 재발송
                                                </button>
                                                
                                                <!-- 인증 타이머 -->
                                                <div id="smsTimer" class="text-warning small" style="display: none;">
                                                    <i class="fas fa-clock me-1"></i>
                                                    남은 시간: <span id="timerDisplay">03:00</span>
                                                </div>
                                            </div>
                                            
                                            <!-- 인증 상태 표시 -->
                                            <div class="mt-2">
                                                <div id="smsSuccess" class="text-success small" style="display: none;">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    SMS 인증이 완료되었습니다.
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 로그인 옵션 -->
                                        <div class="form-group mb-3">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe">
                                                <label class="form-check-label" for="rememberMe">
                                                    로그인 상태 유지
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <!-- 비밀번호 확인 버튼 -->
                                        <button type="button" 
                                                class="btn btn-primary btn-user btn-block w-100 mb-3" 
                                                id="verifyPasswordBtn">
                                            <i class="fas fa-shield-alt me-2"></i>
                                            비밀번호 확인 및 SMS 인증
                                        </button>
                                        
                                        <!-- 로그인 버튼 -->
                                        <button type="submit" 
                                                class="btn btn-success btn-user btn-block w-100" 
                                                id="loginBtn"
                                                disabled
                                                style="display: none;">
                                            <i class="fas fa-sign-in-alt me-2"></i>
                                            로그인 완료
                                        </button>
                                        
                                        <hr>
                                        
                                        <!-- 기타 링크 -->
                                        <div class="text-center">
                                            <a class="small" href="#" id="forgotPasswordLink">비밀번호를 잊으셨나요?</a>
                                        </div>
                                        <div class="text-center mt-2">
                                            <a class="small" href="#" id="contactAdminLink">관리자에게 문의</a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 알림 모달 -->
    <div class="modal fade" id="alertModal" tabindex="-1" aria-labelledby="alertModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="alertModalLabel">알림</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="alertModalBody">
                    <!-- 알림 내용 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">확인</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="loading-overlay" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        /**
         * SMS 통계 시스템 - 로그인 페이지 스크립트
         * 아이디/비밀번호 + SMS 2단계 인증 시스템
         */
        class LoginController {
            constructor() {
                this.isLoading = false;
                this.smsTimer = null;
                this.timerDuration = 180; // 3분 (180초)
                this.currentTime = 0;
                this.isCodeVerified = false;
                
                this.initializeElements();
                this.bindEvents();
                this.initializeForm();
            }

            initializeElements() {
                // Form elements
                this.loginForm = document.getElementById('loginForm');
                this.userIdInput = document.getElementById('userId');
                this.passwordInput = document.getElementById('password');
                this.verificationCodeInput = document.getElementById('verificationCode');
                
                // Buttons
                this.verifyPasswordBtn = document.getElementById('verifyPasswordBtn');
                this.loginBtn = document.getElementById('loginBtn');
                this.resendSmsBtn = document.getElementById('resendSmsBtn');
                this.verifyCodeBtn = document.getElementById('verifyCodeBtn');
                this.togglePasswordBtn = document.getElementById('togglePassword');
                
                // Sections
                this.smsAuthSection = document.getElementById('smsAuthSection');
                this.verificationCodeSection = document.getElementById('verificationCodeSection');
                this.smsTimer = document.getElementById('smsTimer');
                this.smsSuccess = document.getElementById('smsSuccess');
                this.timerDisplay = document.getElementById('timerDisplay');
                this.phoneLastDigits = document.getElementById('phoneLastDigits');
                
                // Modal
                this.alertModal = new bootstrap.Modal(document.getElementById('alertModal'));
                this.alertModalBody = document.getElementById('alertModalBody');
                
                // Loading
                this.loadingSpinner = document.getElementById('loadingSpinner');
                
                // Remember me
                this.rememberMeCheckbox = document.getElementById('rememberMe');
            }

            bindEvents() {
                // Form submission
                this.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
                
                // 비밀번호 확인 및 SMS 발송
                this.verifyPasswordBtn.addEventListener('click', () => this.verifyPasswordAndSendSMS());
                
                // SMS 관련
                this.resendSmsBtn.addEventListener('click', () => this.resendSMSCode());
                this.verifyCodeBtn.addEventListener('click', () => this.verifyCode());
                
                // Password toggle
                this.togglePasswordBtn.addEventListener('click', () => this.togglePassword());
                
                // Verification code input (숫자만 입력)
                this.verificationCodeInput.addEventListener('input', (e) => this.formatVerificationCode(e));
                
                // Enter key handling
                this.verificationCodeInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && this.verificationCodeInput.value.length === 6) {
                        this.verifyCode();
                    }
                });
                
                // 기타 링크들
                document.getElementById('forgotPasswordLink').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.showAlert('비밀번호 재설정', '관리자에게 문의해주세요.');
                });
                
                document.getElementById('contactAdminLink').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.showAlert('관리자 연락처', '시스템 관리자: <EMAIL><br>전화: 02-1234-5678');
                });
            }

            initializeForm() {
                // 저장된 로그인 정보 복원
                const savedUserId = localStorage.getItem('savedUserId');
                if (savedUserId) {
                    this.userIdInput.value = savedUserId;
                    this.rememberMeCheckbox.checked = true;
                }
                
                // 폼 validation 초기화
                this.setupFormValidation();
            }

            setupFormValidation() {
                // 실시간 validation
                this.userIdInput.addEventListener('blur', () => this.validateUserId());
                this.passwordInput.addEventListener('blur', () => this.validatePassword());
            }

            validateUserId() {
                const userId = this.userIdInput.value.trim();
                const isValid = userId.length >= 4;
                
                this.toggleFieldValidation(this.userIdInput, isValid, 
                    isValid ? '올바른 아이디입니다.' : '아이디는 4자 이상이어야 합니다.');
                
                return isValid;
            }

            validatePassword() {
                const password = this.passwordInput.value;
                const isValid = password.length >= 6;
                
                this.toggleFieldValidation(this.passwordInput, isValid, 
                    isValid ? '올바른 비밀번호입니다.' : '비밀번호는 6자 이상이어야 합니다.');
                
                return isValid;
            }

            async verifyPasswordAndSendSMS() {
                // 기본 validation
                const isUserIdValid = this.validateUserId();
                const isPasswordValid = this.validatePassword();
                
                if (!isUserIdValid || !isPasswordValid) {
                    this.showAlert('입력 오류', '아이디와 비밀번호를 올바르게 입력해주세요.');
                    return;
                }

                this.showLoading(true);
                this.verifyPasswordBtn.disabled = true;

                try {
                    // 아이디/비밀번호 검증 및 SMS 발송 요청
                    const loginData = {
                        userId: this.userIdInput.value.trim(),
                        password: this.passwordInput.value
                    };

                    const response = await fetch('/demo/auth/verify-and-send-sms', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(loginData)
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        // 비밀번호가 맞으면 SMS 발송
                        this.registeredPhone = result.phoneNumber;
                        this.phoneLastDigits.textContent = result.phoneNumber.slice(-4);
                        
                        // SMS 인증 섹션 표시
                        this.smsAuthSection.style.display = 'block';
                        this.smsAuthSection.classList.add('active');
                        
                        // 비밀번호 확인 버튼 숨기기
                        this.verifyPasswordBtn.style.display = 'none';
                        
                        // SMS 타이머 시작
                        this.startSMSTimer();
                        this.verificationCodeInput.focus();
                        
                        this.showAlert('인증번호 발송', '등록된 휴대폰 번호로 인증번호가 발송되었습니다. 3분 내에 입력해주세요.');
                        
                    } else {
                        // 비밀번호가 틀리면 초기화
                        this.resetPasswordForm();
                        throw new Error(result.message);
                    }
                } catch (error) {
                    this.showAlert('로그인 실패', error.message || '아이디 또는 비밀번호가 올바르지 않습니다.');
                    this.verifyPasswordBtn.disabled = false;
                } finally {
                    this.showLoading(false);
                }
            }

            resetPasswordForm() {
                // 비밀번호 필드 초기화
                this.passwordInput.value = '';
                this.passwordInput.focus();
                this.passwordInput.classList.remove('is-valid', 'is-invalid');
                
                // SMS 섹션 숨기기
                this.smsAuthSection.style.display = 'none';
                this.smsAuthSection.classList.remove('active');
                this.resetSMSAuth();
                
                // 비밀번호 확인 버튼 다시 표시
                this.verifyPasswordBtn.style.display = 'block';
                this.verifyPasswordBtn.disabled = false;
            }

            toggleFieldValidation(field, isValid, message) {
                field.classList.remove('is-valid', 'is-invalid');
                field.classList.add(isValid ? 'is-valid' : 'is-invalid');
                
                const feedback = field.parentNode.querySelector('.invalid-feedback') || 
                                field.parentNode.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.textContent = message;
                    feedback.className = isValid ? 'valid-feedback' : 'invalid-feedback';
                }
            }

            async resendSMSCode() {
                this.resendSmsBtn.disabled = true;
                this.showLoading(true);

                try {
                    const response = await fetch('/demo/auth/resend-sms', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            userId: this.userIdInput.value.trim()
                        })
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        this.showAlert('인증번호 재발송', '인증번호가 재발송되었습니다. 3분 내에 입력해주세요.');
                        this.startSMSTimer();
                        this.verificationCodeInput.value = '';
                        this.verificationCodeInput.focus();
                    } else {
                        throw new Error(result.message);
                    }
                } catch (error) {
                    this.showAlert('재발송 실패', error.message || 'SMS 재발송에 실패했습니다. 다시 시도해주세요.');
                } finally {
                    this.resendSmsBtn.disabled = false;
                    this.showLoading(false);
                }
            }

            startSMSTimer() {
                this.currentTime = this.timerDuration;
                if (this.smsTimer) {
                    this.smsTimer.style.display = 'block';
                }
                this.updateTimerDisplay();

                this.smsTimerInterval = setInterval(() => {
                    this.currentTime--;
                    this.updateTimerDisplay();

                    if (this.currentTime <= 0) {
                        this.expireSMSCode();
                    }
                }, 1000);
            }

            updateTimerDisplay() {
                if (this.timerDisplay) {
                    const minutes = Math.floor(this.currentTime / 60);
                    const seconds = this.currentTime % 60;
                    this.timerDisplay.textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }

            expireSMSCode() {
                if (this.smsTimerInterval) {
                    clearInterval(this.smsTimerInterval);
                }
                if (this.smsTimer) {
                    this.smsTimer.style.display = 'none';
                }
                if (this.resendSmsBtn) {
                    this.resendSmsBtn.disabled = false;
                    this.resendSmsBtn.innerHTML = '<i class="fas fa-redo me-1"></i>인증번호 재발송';
                }
                if (this.verificationCodeInput) {
                    this.verificationCodeInput.value = '';
                    this.verificationCodeInput.disabled = true;
                }
                if (this.verifyCodeBtn) {
                    this.verifyCodeBtn.disabled = true;
                }
                this.showAlert('인증 시간 만료', '인증 시간이 만료되었습니다. 재발송 버튼을 클릭해주세요.');
            }

            async verifyCode() {
                const code = this.verificationCodeInput.value.trim();
                
                if (code.length !== 6) {
                    this.showAlert('입력 오류', '6자리 인증번호를 입력해주세요.');
                    return;
                }

                this.showLoading(true);
                this.verifyCodeBtn.disabled = true;

                try {
                    const response = await fetch('/demo/auth/verify-sms', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            userId: this.userIdInput.value.trim(),
                            verificationCode: code
                        })
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        this.isCodeVerified = true;
                        if (this.smsTimerInterval) {
                            clearInterval(this.smsTimerInterval);
                        }
                        if (this.smsTimer) {
                            this.smsTimer.style.display = 'none';
                        }
                        if (this.smsSuccess) {
                            this.smsSuccess.style.display = 'block';
                        }
                        if (this.verificationCodeInput) {
                            this.verificationCodeInput.disabled = true;
                        }
                        if (this.verifyCodeBtn) {
                            this.verifyCodeBtn.innerHTML = '<i class="fas fa-check me-1"></i>인증 완료';
                            this.verifyCodeBtn.disabled = true;
                            this.verifyCodeBtn.classList.remove('btn-success');
                            this.verifyCodeBtn.classList.add('btn-outline-success');
                        }
                        if (this.resendSmsBtn) {
                            this.resendSmsBtn.style.display = 'none';
                        }
                        
                        // 로그인 버튼 활성화 및 표시
                        this.enableLoginButton();
                        this.showAlert('인증 완료', 'SMS 인증이 완료되었습니다. 로그인 버튼을 클릭하세요.');
                    } else {
                        throw new Error(result.message);
                    }
                } catch (error) {
                    this.showAlert('인증 실패', error.message || '인증번호가 올바르지 않습니다.');
                    if (this.verificationCodeInput) {
                        this.verificationCodeInput.value = '';
                        this.verificationCodeInput.focus();
                    }
                    this.verifyCodeBtn.disabled = false;
                } finally {
                    this.showLoading(false);
                }
            }

            async handleLogin(e) {
                e.preventDefault();
                
                if (this.isLoading) return;

                // SMS 인증 확인 (필수)
                if (!this.isCodeVerified) {
                    this.showAlert('인증 필요', 'SMS 인증을 완료해주세요.');
                    return;
                }

                this.showLoading(true);

                try {
                    // Remember me 처리
                    if (this.rememberMeCheckbox.checked) {
                        localStorage.setItem('savedUserId', this.userIdInput.value.trim());
                    } else {
                        localStorage.removeItem('savedUserId');
                    }

                    // 폼 전송 (서버에서 최종 로그인 처리)
                    this.loginForm.submit();
                    
                } catch (error) {
                    this.showAlert('로그인 실패', error.message || '로그인에 실패했습니다. 다시 시도해주세요.');
                    this.showLoading(false);
                }
            }

            resetSMSAuth() {
                if (this.smsTimerInterval) {
                    clearInterval(this.smsTimerInterval);
                }
                this.isCodeVerified = false;
                if (this.smsTimer) {
                    this.smsTimer.style.display = 'none';
                }
                if (this.smsSuccess) {
                    this.smsSuccess.style.display = 'none';
                }
                if (this.verificationCodeInput) {
                    this.verificationCodeInput.value = '';
                    this.verificationCodeInput.disabled = false;
                }
                if (this.verifyCodeBtn) {
                    this.verifyCodeBtn.disabled = false;
                    this.verifyCodeBtn.innerHTML = '<i class="fas fa-check me-1"></i>인증 확인';
                    this.verifyCodeBtn.classList.remove('btn-outline-success');
                    this.verifyCodeBtn.classList.add('btn-success');
                }
                if (this.resendSmsBtn) {
                    this.resendSmsBtn.disabled = false;
                    this.resendSmsBtn.innerHTML = '<i class="fas fa-redo me-1"></i>인증번호 재발송';
                    this.resendSmsBtn.style.display = 'inline-block';
                }
                
                // 로그인 버튼 숨기기
                if (this.loginBtn) {
                    this.loginBtn.style.display = 'none';
                    this.loginBtn.disabled = true;
                }
            }

            enableLoginButton() {
                this.loginBtn.disabled = false;
                this.loginBtn.style.display = 'block';
                this.loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>로그인 완료';
                this.loginBtn.classList.remove('btn-secondary');
                this.loginBtn.classList.add('btn-success');
            }

            togglePassword() {
                const type = this.passwordInput.type === 'password' ? 'text' : 'password';
                this.passwordInput.type = type;
                
                const icon = this.togglePasswordBtn.querySelector('i');
                icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
            }

            formatVerificationCode(e) {
                e.target.value = e.target.value.replace(/[^\d]/g, '').slice(0, 6);
            }

            showLoading(show) {
                this.isLoading = show;
                this.loadingSpinner.style.display = show ? 'flex' : 'none';
            }

            showAlert(title, message, callback = null) {
                document.getElementById('alertModalLabel').textContent = title;
                this.alertModalBody.innerHTML = message;
                
                if (callback) {
                    // 모달이 닫힐 때 콜백 실행
                    const modal = document.getElementById('alertModal');
                    modal.addEventListener('hidden.bs.modal', callback, { once: true });
                }
                
                this.alertModal.show();
            }
        }

        // 페이지 로드 시 초기화
        document.addEventListener('DOMContentLoaded', () => {
            window.loginController = new LoginController();
            
            // 개발용 테스트 정보 표시 (프로덕션에서는 제거)
            console.log('=== 개발용 테스트 계정 ===');
            console.log('아이디: admin');
            console.log('비밀번호: admin123');
            console.log('SMS 인증번호: 123456');
            console.log('========================');
        });
    </script>
</body>
</html>